

## 1.1.1 / 2016-02-23
- Release v1.1.1 / npm@v1.1.1
- update license year
- update semver to `^5.1.0`
- add support for scoped shorthands e.g. `@user/package@~2.2.5` (PR [#2](https://github.com/tunnckoCore/parse-semver/pull/2 "Support scoped packages"))

## 1.1.0 / 2015-07-19
- Release v1.1.0 / npm@v1.1.0
- add related section
- update docs
- bump `semver@5`
- update

## 1.0.1 / 2015-04-17
- Release v1.0.1 / npm@v1.0.1
- bump `semver@~4.3.3`

## 1.0.0 / 2015-03-26
- Release v1.0.0 / npm@v1.0.0
- run keywords(1)
- run docks(1)
- **update** deps, polyfill

## 0.0.0 / 2015-03-26
- first commits / npm@v0.0.0