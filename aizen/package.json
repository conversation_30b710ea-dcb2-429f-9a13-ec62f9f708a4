{"name": "aizen", "version": "1.83.0", "distro": "e61050c5d43d2c66a196fafaa2b78d4f2a8c929a", "author": {"name": "OGbikram0001"}, "license": "MIT", "main": "./out/main", "private": true, "scripts": {"test": "echo Please run any of the test scripts from the scripts folder.", "test-browser": "npx playwright install && node test/unit/browser/index.js", "test-browser-no-install": "node test/unit/browser/index.js", "test-node": "mocha test/unit/node/index.js --delay --ui=tdd --timeout=5000 --exit", "preinstall": "node build/npm/preinstall.js", "postinstall": "node build/npm/postinstall.js", "compile": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js compile", "watch": "npm-run-all -lp watch-client watch-extensions", "watchd": "deemon yarn watch", "watch-webd": "deemon yarn watch-web", "kill-watchd": "deemon --kill yarn watch", "kill-watch-webd": "deemon --kill yarn watch-web", "restart-watchd": "deemon --restart yarn watch", "restart-watch-webd": "deemon --restart yarn watch-web", "watch-client": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js watch-client", "watch-clientd": "deemon yarn watch-client", "kill-watch-clientd": "deemon --kill yarn watch-client", "watch-extensions": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js watch-extensions watch-extension-media", "watch-extensionsd": "deemon yarn watch-extensions", "kill-watch-extensionsd": "deemon --kill yarn watch-extensions", "precommit": "node build/hygiene.js", "gulp": "node --max_old_space_size=8192 ./node_modules/gulp/bin/gulp.js", "electron": "node build/lib/electron", "7z": "7z", "update-grammars": "node build/npm/update-all-grammars.mjs", "update-localization-extension": "node build/npm/update-localization-extension.js", "smoketest": "node build/lib/preLaunch.js && cd test/smoke && yarn compile && node test/index.js", "smoketest-no-compile": "cd test/smoke && node test/index.js", "download-builtin-extensions": "node build/lib/builtInExtensions.js", "download-builtin-extensions-cg": "node build/lib/builtInExtensionsCG.js", "monaco-compile-check": "tsc -p src/tsconfig.monaco.json --noEmit", "tsec-compile-check": "node node_modules/tsec/bin/tsec -p src/tsconfig.tsec.json", "vscode-dts-compile-check": "tsc -p src/tsconfig.vscode-dts.json && tsc -p src/tsconfig.vscode-proposed-dts.json", "valid-layers-check": "node build/lib/layersChecker.js", "update-distro": "node build/npm/update-distro.mjs", "web": "echo 'yarn web' is replaced by './scripts/code-server' or './scripts/code-web'", "compile-cli": "gulp compile-cli", "compile-web": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js compile-web", "watch-web": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js watch-web", "watch-cli": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js watch-cli", "eslint": "node build/eslint", "stylelint": "node build/stylelint", "playwright-install": "node build/azure-pipelines/common/installPlaywright.js", "compile-build": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js compile-build", "compile-extensions-build": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js compile-extensions-build", "minify-vscode": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js minify-vscode", "minify-vscode-reh": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js minify-vscode-reh", "minify-vscode-reh-web": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js minify-vscode-reh-web", "hygiene": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js hygiene", "core-ci": "node --max_old_space_size=8095 ./node_modules/gulp/bin/gulp.js core-ci", "core-ci-pr": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js core-ci-pr", "extensions-ci": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js extensions-ci", "extensions-ci-pr": "node --max_old_space_size=4095 ./node_modules/gulp/bin/gulp.js extensions-ci-pr", "perf": "node scripts/code-perf.js"}, "dependencies": {"@azure/msal-node": "^3.6.1", "@microsoft/1ds-core-js": "^3.2.13", "@microsoft/1ds-post-js": "^3.2.13", "@parcel/watcher": "2.1.0", "@types/markdown-it": "^14.1.2", "@types/morphdom": "^2.4.2", "@types/parse5": "^7.0.0", "@types/which": "^3.0.4", "@vscode/extension-telemetry": "^1.0.0", "@vscode/iconv-lite-umd": "0.7.0", "@vscode/l10n": "^0.0.18", "@vscode/policy-watcher": "^1.1.4", "@vscode/proxy-agent": "^0.17.4", "@vscode/ripgrep": "^1.15.5", "@vscode/spdlog": "^0.13.11", "@vscode/sqlite3": "5.1.6-vscode", "@vscode/sudo-prompt": "9.3.1", "@vscode/sync-api-client": "^0.8.1", "@vscode/sync-api-common": "^0.8.1", "@vscode/sync-api-service": "^0.8.1", "@vscode/vscode-languagedetection": "1.0.21", "@vscode/windows-mutex": "^0.4.4", "@vscode/windows-process-tree": "^0.5.0", "@vscode/windows-registry": "^1.1.0", "dompurify": "^3.2.6", "esbuild": "^0.25.5", "graceful-fs": "4.2.11", "gulp-merge-json": "^2.2.1", "gulp-shell": "^0.8.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.3", "jschardet": "3.0.0", "jsdom": "^26.1.0", "jsonc-parser": "^3.3.1", "kerberos": "^2.0.1", "markdown-it": "^14.1.0", "minimist": "^1.2.6", "morphdom": "^2.7.5", "native-is-elevated": "0.7.0", "native-keymap": "^3.3.4", "native-watchdog": "^1.4.1", "node-pty": "1.1.0-beta1", "parse-semver": "^1.1.1", "parse5-sax-parser": "^7.0.0", "request-light": "^0.8.0", "tas-client-umd": "0.1.8", "ternary-stream": "^3.0.0", "v8-inspect-profiler": "^0.1.0", "vscode-gulp-watch": "^5.0.3", "vscode-json-languageservice": "^5.6.0", "vscode-languageclient": "^9.0.1", "vscode-languageserver": "^9.0.1", "vscode-oniguruma": "1.7.0", "vscode-regexpp": "^3.1.0", "vscode-textmate": "9.0.0", "xterm": "5.4.0-beta.27", "xterm-addon-canvas": "0.6.0-beta.27", "xterm-addon-image": "0.6.0-beta.21", "xterm-addon-search": "0.14.0-beta.27", "xterm-addon-serialize": "0.12.0-beta.26", "xterm-addon-unicode11": "0.7.0-beta.26", "xterm-addon-webgl": "0.17.0-beta.26", "xterm-headless": "5.4.0-beta.27", "yauzl": "^2.9.2", "yazl": "^2.4.3"}, "devDependencies": {"@playwright/test": "^1.37.1", "@swc/cli": "0.1.62", "@swc/core": "1.3.62", "@types/cookie": "^0.3.3", "@types/cssnano": "^4.0.0", "@types/debug": "4.1.5", "@types/graceful-fs": "4.1.2", "@types/gulp-postcss": "^8.0.0", "@types/gulp-svgmin": "^1.2.1", "@types/http-proxy-agent": "^2.0.1", "@types/kerberos": "^1.1.2", "@types/minimist": "^1.2.1", "@types/mocha": "^9.1.1", "@types/node": "^24.0.7", "@types/sinon": "^10.0.2", "@types/sinon-test": "^2.4.2", "@types/trusted-types": "^1.0.6", "@types/vscode-notebook-renderer": "^1.72.0", "@types/webpack": "^5.28.1", "@types/wicg-file-system-access": "^2020.9.6", "@types/windows-foreground-love": "^0.3.0", "@types/winreg": "^1.2.30", "@types/yauzl": "^2.9.1", "@types/yazl": "^2.4.2", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/experimental-utils": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "@vscode/gulp-electron": "^1.36.0", "@vscode/l10n-dev": "0.0.21", "@vscode/telemetry-extractor": "^1.9.9", "@vscode/test-web": "^0.0.41", "@vscode/vscode-perf": "^0.0.14", "ansi-colors": "^3.2.3", "asar": "^3.0.3", "chromium-pickle-js": "^0.2.0", "cookie": "^0.4.0", "copy-webpack-plugin": "^11.0.0", "cson-parser": "^1.3.3", "css-loader": "^6.7.3", "cssnano": "^4.1.11", "debounce": "^1.0.0", "deemon": "^1.8.0", "electron": "^25.8.2", "eslint": "8.36.0", "eslint-plugin-header": "3.1.1", "eslint-plugin-jsdoc": "^46.5.0", "eslint-plugin-local": "^1.0.0", "event-stream": "3.3.4", "fancy-log": "^1.3.3", "fast-plist": "0.1.3", "file-loader": "^6.2.0", "glob": "^5.0.13", "gulp": "^4.0.0", "gulp-azure-storage": "^0.12.1", "gulp-bom": "^3.0.0", "gulp-buffer": "0.0.2", "gulp-concat": "^2.6.1", "gulp-eslint": "^5.0.0", "gulp-filter": "^5.1.0", "gulp-flatmap": "^1.0.2", "gulp-gunzip": "^1.0.0", "gulp-gzip": "^1.4.2", "gulp-json-editor": "^2.5.0", "gulp-plumber": "^1.2.0", "gulp-postcss": "^9.0.0", "gulp-rename": "^1.2.0", "gulp-replace": "^0.5.4", "gulp-sourcemaps": "^3.0.0", "gulp-svgmin": "^4.1.0", "gulp-untar": "^0.0.7", "husky": "^0.13.1", "innosetup": "6.0.5", "is": "^3.1.0", "istanbul-lib-coverage": "^3.2.0", "istanbul-lib-instrument": "^5.2.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.1.5", "lazy.js": "^0.4.2", "merge-options": "^1.0.1", "mime": "^1.4.1", "minimatch": "^3.0.4", "minimist": "^1.2.6", "mkdirp": "^1.0.4", "mocha": "^9.2.2", "mocha-junit-reporter": "^2.0.0", "mocha-multi-reporters": "^1.5.1", "npm-run-all": "^4.1.5", "opn": "^6.0.0", "optimist": "0.3.5", "p-all": "^1.0.0", "path-browserify": "^1.0.1", "pump": "^1.0.1", "queue": "3.0.6", "rcedit": "^1.1.0", "rimraf": "^2.2.8", "sinon": "^12.0.1", "sinon-test": "^3.1.3", "source-map": "0.6.1", "source-map-support": "^0.3.2", "style-loader": "^3.3.2", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsec": "0.2.7", "typescript": "5.9.0-dev.20250613", "typescript-formatter": "7.1.0", "underscore": "^1.12.1", "util": "^0.12.4", "vscode-nls-dev": "^3.3.1", "webpack": "^5.77.0", "webpack-cli": "^5.0.1", "webpack-stream": "^7.0.0", "xml2js": "^0.5.0", "yaserver": "^0.4.0"}, "repository": {"type": "git", "url": "https://github.com/OGbikram0001/aizen.git"}, "bugs": {"url": "https://github.com/OGbikram0001/aizen/issues"}, "optionalDependencies": {"windows-foreground-love": "0.5.0"}}