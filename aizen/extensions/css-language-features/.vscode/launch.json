{"version": "0.2.0", "configurations": [{"name": "Launch Extension", "type": "extensionHost", "request": "launch", "runtimeExecutable": "${execPath}", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "stopOnEntry": false, "sourceMaps": true, "outFiles": ["${workspaceFolder}/client/out/**/*.js"], "smartStep": true}, {"name": "Launch Tests", "type": "extensionHost", "request": "launch", "runtimeExecutable": "${execPath}", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--extensionTestsPath=${workspaceFolder}/client/out/test"], "stopOnEntry": false, "sourceMaps": true, "outFiles": ["${workspaceFolder}/client/out/test/**/*.js"]}, {"name": "Server Unit Tests", "type": "node", "request": "launch", "program": "${workspaceRoot}/node_modules/mocha/bin/_mocha", "stopOnEntry": false, "args": ["--timeout", "999999", "--colors"], "cwd": "${workspaceRoot}", "runtimeExecutable": null, "runtimeArgs": [], "env": {}, "sourceMaps": true, "outFiles": ["${workspaceRoot}/server/out/**"]}]}