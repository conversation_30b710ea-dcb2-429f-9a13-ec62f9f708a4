{"name": "vscode-css-languageserver", "description": "CSS/LESS/SCSS language server", "version": "1.0.0", "author": "Microsoft Corporation", "license": "MIT", "engines": {"node": "*"}, "main": "./out/node/cssServerMain", "browser": "./dist/browser/cssServerMain", "dependencies": {"@vscode/l10n": "^0.0.16", "vscode-css-languageservice": "^6.2.9", "vscode-languageserver": "^8.2.0-next.3", "vscode-uri": "^3.0.7"}, "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "18.x"}, "scripts": {"compile": "gulp compile-extension:css-language-features-server", "watch": "gulp watch-extension:css-language-features-server", "install-service-next": "yarn add vscode-css-languageservice@next", "install-service-local": "yarn link vscode-css-languageservice", "install-server-next": "yarn add vscode-languageserver@next", "install-server-local": "yarn link vscode-languageserver", "test": "node ./test/index.js"}}