# Grunt - The JavaScript Task Runner

**Notice:** This extension is bundled with Visual Studio Code. It can be disabled but not uninstalled.

## Features

This extension supports running [Grunt](https://gruntjs.com/) tasks defined in a `gruntfile.js` file as [VS Code tasks](https://code.visualstudio.com/docs/editor/tasks). Grunt tasks with the name 'build', 'compile', or 'watch' are treated as build tasks.

To run Grunt tasks, use the **Tasks** menu.

## Settings

- `grunt.autoDetect` - Enable detecting tasks from `gruntfile.js` files, the default is `on`.
