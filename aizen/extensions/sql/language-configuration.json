{
	"comments": {
		"lineComment": "--",
		"blockComment": [ "/*", "*/" ]
	},
	"brackets": [
		["{", "}"],
		["[", "]"],
		["(", ")"]
	],
	"autoClosingPairs": [
		["{", "}"],
		["[", "]"],
		["(", ")"],
		{ "open": "\"", "close": "\"", "notIn": ["string"] },
		{ "open": "N'", "close": "'", "notIn": ["string", "comment"] },
		{ "open": "'", "close": "'", "notIn": ["string", "comment"] }
	],
	"surroundingPairs": [
		["{", "}"],
		["[", "]"],
		["(", ")"],
		["\"", "\""],
		["'", "'"],
		["`", "`"]
	],
	"folding": {
		"offSide": true,
		"markers": {
			"start": "^\\s*--\\s*#region\\b",
			"end": "^\\s*--\\s*#endregion\\b"
		}
	},

	// enhancedBrackets:[
	// 	{ openTrigger: 'n', open: /begin$/i, closeComplete: 'end', matchCase: true },
	// 	{ openTrigger: 'e', open: /case$/i, closeComplete: 'end', matchCase: true },
	// 	{ openTrigger: 'n', open: /when$/i, closeComplete: 'then', matchCase: true }
	// ],
	// noindentBrackets: '()',
}
