{
	"comments": {
		"blockComment": [ "{{!--", "--}}" ]
	},
	"brackets": [
		["<!--", "-->"],
		["<", ">"],
		["{{", "}}"],
		["{{{", "}}}"],
		["{", "}"],
		["(", ")"]
	],
	"autoClosingPairs": [
		{ "open": "{", "close": "}"},
		{ "open": "[", "close": "]"},
		{ "open": "(", "close": ")" },
		{ "open": "'", "close": "'" },
		{ "open": "\"", "close": "\"" }
	],
	"surroundingPairs": [
		{ "open": "'", "close": "'" },
		{ "open": "\"", "close": "\"" },
		{ "open": "<", "close": ">" },
		{ "open": "{", "close": "}" }
	],
	"wordPattern": "(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)",
	"onEnterRules": [
		{
			"beforeText": { "pattern": "<(?!(?:area|base|br|col|embed|hr|img|input|keygen|link|menuitem|meta|param|source|track|wbr))([_:\\w][_:\\w-.\\d]*)([^/>]*(?!\\/)>)[^<]*$", "flags": "i" },
			"afterText": { "pattern": "^<\\/([_:\\w][_:\\w-.\\d]*)\\s*>", "flags": "i" },
			"action": {
				"indent": "indentOutdent"
			}
		},
		{
			"beforeText": { "pattern": "<(?!(?:area|base|br|col|embed|hr|img|input|keygen|link|menuitem|meta|param|source|track|wbr))(\\w[\\w\\d]*)([^/>]*(?!\\/)>)[^<]*$", "flags": "i" },
			"action": {
				"indent": "indent"
			}
		}
	],
}
