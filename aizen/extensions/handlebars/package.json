{"name": "handlebars", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "0.10.x"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin daaain/Handlebars grammars/Handlebars.json ./syntaxes/Handlebars.tmLanguage.json"}, "extensionKind": ["ui", "workspace"], "contributes": {"languages": [{"id": "handlebars", "extensions": [".handlebars", ".hbs", ".hjs"], "aliases": ["Handlebars", "handlebars"], "mimetypes": ["text/x-handlebars-template"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "handlebars", "scopeName": "text.html.handlebars", "path": "./syntaxes/Handlebars.tmLanguage.json"}], "htmlLanguageParticipants": [{"languageId": "handlebars", "autoInsert": true}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}