/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

html {
	width: 100%;
	height: 100%;
	text-align: center;
}

body {
	padding: 5px 10px;
	width: calc(100% - 20px);
	height: calc(100% - 10px);
	display: flex;
	justify-content: center;
	align-items: center;
	-webkit-user-select: none;
	user-select: none;
}

.loading-indicator {
	width: 30px;
	height: 30px;
	background-image: url('./loading.svg');
	background-size: cover;
}

.loading-indicator,
.loading-error {
	display: none;
}

.loading .loading-indicator,
.error .loading-error {
	display: block;
}

.loading-error {
	margin: 1em;
}

.vscode-dark .loading-indicator {
	background-image: url('./loading-dark.svg');
}

.vscode-high-contrast .loading-indicator {
	background-image: url('./loading-hc.svg');
}
