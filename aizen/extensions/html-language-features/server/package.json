{"name": "vscode-html-languageserver", "description": "HTML language server", "version": "1.0.0", "author": "Microsoft Corporation", "license": "MIT", "engines": {"node": "*"}, "main": "./out/node/htmlServerMain", "dependencies": {"@vscode/l10n": "^0.0.16", "vscode-css-languageservice": "^6.2.9", "vscode-html-languageservice": "^5.1.0", "vscode-languageserver": "^8.2.0-next.3", "vscode-languageserver-textdocument": "^1.0.8", "vscode-uri": "^3.0.7"}, "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "18.x"}, "scripts": {"compile": "npx gulp compile-extension:html-language-features-server", "watch": "npx gulp watch-extension:html-language-features-server", "install-service-next": "yarn add vscode-css-languageservice@next && yarn add vscode-html-languageservice@next", "install-service-local": "yarn link vscode-css-languageservice && yarn link vscode-html-languageservice", "install-server-next": "yarn add vscode-languageserver@next", "install-server-local": "yarn link vscode-languageserver", "test": "yarn compile && node ./test/index.js"}}