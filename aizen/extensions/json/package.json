{"name": "json", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "0.10.x"}, "scripts": {"update-grammar": "node ./build/update-grammars.js"}, "contributes": {"languages": [{"id": "json", "aliases": ["JSON", "json"], "extensions": [".json", ".bower<PERSON>", ".jscsrc", ".webmanifest", ".js.map", ".css.map", ".ts.map", ".har", ".jslintrc", ".j<PERSON><PERSON>", ".geo<PERSON><PERSON>", ".ipynb", ".vuerc"], "filenames": ["composer.lock", ".watchmanconfig"], "mimetypes": ["application/json", "application/manifest+json"], "configuration": "./language-configuration.json"}, {"id": "jsonc", "aliases": ["JSON with Comments"], "extensions": [".jsonc", ".eslintrc", ".eslintrc.json", ".jsfmtrc", ".j<PERSON>trc", ".swcrc", ".hintrc", ".babelrc"], "filenames": ["babel.config.json", ".babelrc.json", ".ember-cli", "typedoc.json"], "configuration": "./language-configuration.json"}, {"id": "jsonl", "aliases": ["JSON Lines"], "extensions": [".j<PERSON>l"], "filenames": [], "configuration": "./language-configuration.json"}, {"id": "snippets", "aliases": ["Code Snippets"], "extensions": [".code-snippets"], "filenamePatterns": ["**/User/snippets/*.json", "**/User/profiles/*/snippets/*.json"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "json", "scopeName": "source.json", "path": "./syntaxes/JSON.tmLanguage.json"}, {"language": "jsonc", "scopeName": "source.json.comments", "path": "./syntaxes/JSONC.tmLanguage.json"}, {"language": "jsonl", "scopeName": "source.json.lines", "path": "./syntaxes/JSONL.tmLanguage.json"}, {"language": "snippets", "scopeName": "source.json.comments.snippets", "path": "./syntaxes/snippets.tmLanguage.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}