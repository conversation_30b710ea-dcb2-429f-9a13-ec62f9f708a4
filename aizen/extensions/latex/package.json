{"name": "latex", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ./build/update-grammars.js"}, "contributes": {"languages": [{"id": "tex", "aliases": ["TeX", "tex"], "extensions": [".sty", ".cls", ".bbx", ".cbx"], "configuration": "latex-language-configuration.json"}, {"id": "latex", "aliases": ["LaTeX", "latex"], "extensions": [".tex", ".ltx", ".ctx"], "configuration": "latex-language-configuration.json"}, {"id": "bibtex", "aliases": ["BibTeX", "bibtex"], "extensions": [".bib"]}, {"id": "cpp_embedded_latex", "configuration": "latex-cpp-embedded-language-configuration.json", "aliases": []}, {"id": "markdown_latex_combined", "configuration": "markdown-latex-combined-language-configuration.json", "aliases": []}], "grammars": [{"language": "tex", "scopeName": "text.tex", "path": "./syntaxes/TeX.tmLanguage.json"}, {"language": "latex", "scopeName": "text.tex.latex", "path": "./syntaxes/LaTeX.tmLanguage.json", "embeddedLanguages": {"source.cpp": "cpp_embedded_latex", "source.css": "css", "text.html": "html", "source.java": "java", "source.js": "javascript", "source.julia": "julia", "source.lua": "lua", "source.python": "python", "source.ruby": "ruby", "source.ts": "typescript", "text.xml": "xml", "source.yaml": "yaml", "meta.embedded.markdown_latex_combined": "markdown_latex_combined"}}, {"language": "bibtex", "scopeName": "text.bibtex", "path": "./syntaxes/Bibtex.tmLanguage.json"}, {"language": "markdown_latex_combined", "scopeName": "text.tex.markdown_latex_combined", "path": "./syntaxes/markdown-latex-combined.tmLanguage.json"}, {"language": "cpp_embedded_latex", "scopeName": "source.cpp.embedded.latex", "path": "./syntaxes/cpp-grammar-bailout.tmLanguage.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}