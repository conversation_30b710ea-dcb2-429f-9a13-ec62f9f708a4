{"comments": {"blockComment": ["<!--", "-->"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"], ["[", ")"], ["(", "]"], ["\\left(", "\\right)"], ["\\left(", "\\right."], ["\\left.", "\\right)"], ["\\left[", "\\right]"], ["\\left[", "\\right."], ["\\left.", "\\right]"], ["\\left\\{", "\\right\\}"], ["\\left\\{", "\\right."], ["\\left.", "\\right\\}"], ["\\left<", "\\right>"], ["\\bigl(", "\\bigr)"], ["\\bigl[", "\\bigr]"], ["\\bigl\\{", "\\bigr\\}"], ["\\Bigl(", "\\Bigr)"], ["\\Bigl[", "\\Bigr]"], ["\\Bigl\\{", "\\Bigr\\}"], ["\\biggl(", "\\biggr)"], ["\\biggl[", "\\biggr]"], ["\\biggl\\{", "\\biggr\\}"], ["\\Biggl(", "\\Biggr)"], ["\\Biggl[", "\\Biggr]"], ["\\Biggl\\{", "\\Biggr\\}"], ["\\langle", "\\rangle"], ["\\lvert", "\\rvert"], ["\\lVert", "\\rVert"], ["\\left|", "\\right|"], ["\\left\\vert", "\\right\\vert"], ["\\left\\|", "\\right\\|"], ["\\left\\Vert", "\\right\\Vert"], ["\\left\\langle", "\\right\\rangle"], ["\\left\\lvert", "\\right\\rvert"], ["\\left\\lVert", "\\right\\rVert"], ["\\bigl\\langle", "\\bigr\\rangle"], ["\\bigl|", "\\bigr|"], ["\\bigl\\vert", "\\bigr\\vert"], ["\\bigl\\lvert", "\\bigr\\rvert"], ["\\bigl\\|", "\\bigr\\|"], ["\\bigl\\lVert", "\\bigr\\rVert"], ["\\bigl\\Vert", "\\bigr\\Vert"], ["\\Bigl\\langle", "\\Bigr\\rangle"], ["\\Bigl|", "\\Bigr|"], ["\\Bigl\\lvert", "\\Bigr\\rvert"], ["\\Bigl\\vert", "\\Bigr\\vert"], ["\\Bigl\\|", "\\Bigr\\|"], ["\\Bigl\\lVert", "\\Bigr\\rVert"], ["\\Bigl\\Vert", "\\Bigr\\Vert"], ["\\biggl\\langle", "\\biggr\\rangle"], ["\\biggl|", "\\biggr|"], ["\\biggl\\lvert", "\\biggr\\rvert"], ["\\biggl\\vert", "\\biggr\\vert"], ["\\biggl\\|", "\\biggr\\|"], ["\\biggl\\lVert", "\\biggr\\rVert"], ["\\biggl\\Vert", "\\biggr\\Vert"], ["\\Biggl\\langle", "\\Biggr\\rangle"], ["\\Biggl|", "\\Biggr|"], ["\\Biggl\\lvert", "\\Biggr\\rvert"], ["\\Biggl\\vert", "\\Biggr\\vert"], ["\\Biggl\\|", "\\Biggr\\|"], ["\\Biggl\\lVert", "\\Biggr\\rVert"], ["\\Biggl\\Vert", "\\Biggr\\Vert"]], "autoClosingPairs": [["\\left(", "\\right)"], ["\\left[", "\\right]"], ["\\left\\{", "\\right\\}"], ["\\bigl(", "\\bigr)"], ["\\bigl[", "\\bigr]"], ["\\bigl\\{", "\\bigr\\}"], ["\\Bigl(", "\\Bigr)"], ["\\Bigl[", "\\Bigr]"], ["\\Bigl\\{", "\\Bigr\\}"], ["\\biggl(", "\\biggr)"], ["\\biggl[", "\\biggr]"], ["\\biggl\\{", "\\biggr\\}"], ["\\Biggl(", "\\Biggr)"], ["\\Biggl[", "\\Biggr]"], ["\\Biggl\\{", "\\Biggr\\}"], ["\\(", "\\)"], ["\\[", "\\]"], ["\\{", "\\}"], ["{", "}"], ["[", "]"], ["(", ")"], ["`", "'"]], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"], ["$", "$"], ["`", "`"], ["_", "_"], ["*", "*"]], "indentationRules": {"increaseIndentPattern": "\\\\begin{(?!document)([^}]*)}(?!.*\\\\end{\\1})", "decreaseIndentPattern": "^\\s*\\\\end{(?!document)"}, "folding": {"offSide": true, "markers": {"start": "^\\s*<!--\\s*#?region\\b.*-->", "end": "^\\s*<!--\\s*#?endregion\\b.*-->"}}, "autoCloseBefore": ";:.,={}])>\\` \n\t$", "wordPattern": {"pattern": "([*_]{1,2})?(\\p{Alphabetic}|\\p{Number}|\\p{Nonspacing_Mark})(((\\p{Alphabetic}|\\p{Number}|\\p{Nonspacing_Mark})|[_])?(\\p{Alphabetic}|\\p{Number}|\\p{Nonspacing_Mark}))*\\1", "flags": "u"}}