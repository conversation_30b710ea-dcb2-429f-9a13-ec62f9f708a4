/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Csp<PERSON>ler<PERSON> } from './csp';
import { StyleLoadingMonitor } from './loading';
import { SettingsManager } from './settings';

declare global {
	interface Window {
		cspAlerter: CspAlerter;
		styleLoadingMonitor: StyleLoadingMonitor;
	}
}

window.cspAlerter = new CspAlerter(new SettingsManager());
window.styleLoadingMonitor = new StyleLoadingMonitor();
