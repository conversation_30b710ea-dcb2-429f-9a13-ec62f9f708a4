test/**
test-workspace/**
src/**
notebook/**
tsconfig.json
tsconfig.*.json
out/test/**
out/**
extension.webpack.config.js
extension-browser.webpack.config.js
cgmanifest.json
yarn.lock
preview-src/**
webpack.config.js
esbuild-notebook.js
esbuild-preview.js
.gitignore
server/src/**
server/extension.webpack.config.js
server/extension-browser.webpack.config.js
server/tsconfig.json
server/.vscode/**
server/node_modules/**
server/yarn.lock
server/.npmignore
