name: $(Date:yyyyMMdd)$(Rev:.r)

trigger: none
pr: none

resources:
  repositories:
    - repository: templates
      type: github
      name: microsoft/vscode-engineering
      ref: main
      endpoint: Monaco

parameters:
  - name: publishPackage
    displayName: Publish vscode-markdown-languageserver
    type: boolean
    default: false

extends:
  template: azure-pipelines/npm-package/pipeline.yml@templates
  parameters:
    npmPackages:
      - name: vscode-markdown-languageserver
        workingDirectory: extensions/markdown-language-features/server

        buildSteps:
          - script: yarn install
            displayName: Install dependencies

          - script: gulp compile-extension:markdown-language-features-server
            displayName: Compile

        publishPackage: ${{ parameters.publishPackage }}
