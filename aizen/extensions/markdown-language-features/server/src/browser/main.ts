/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { B<PERSON>er<PERSON><PERSON>ageReader, BrowserMessageWriter, createConnection } from 'vscode-languageserver/browser';
import { startVsCodeServer } from '../server';

const messageReader = new BrowserMessageReader(self);
const messageWriter = new BrowserMessageWriter(self);

const connection = createConnection(messageReader, messageWriter);

startVsCodeServer(connection);
