{"name": "vscode-markdown-languageserver", "description": "Markdown language server", "version": "0.4.0-alpha.6", "author": "Microsoft Corporation", "license": "MIT", "engines": {"node": "*"}, "main": "./out/node/main", "browser": "./dist/browser/main", "files": ["dist/**/*.js", "out/**/*.js"], "dependencies": {"@vscode/l10n": "^0.0.11", "vscode-languageserver": "^8.1.0", "vscode-languageserver-textdocument": "^1.0.8", "vscode-languageserver-types": "^3.17.3", "vscode-markdown-languageservice": "^0.4.0-alpha.6", "vscode-uri": "^3.0.7"}, "devDependencies": {"@types/node": "18.x"}, "scripts": {"compile": "gulp compile-extension:markdown-language-features-server", "prepublishOnly": "npm run compile", "watch": "gulp watch-extension:markdown-language-features-server", "compile-web": "npx webpack-cli --config extension-browser.webpack.config --mode none", "watch-web": "npx webpack-cli --config extension-browser.webpack.config --mode none --watch --info-verbosity verbose"}}