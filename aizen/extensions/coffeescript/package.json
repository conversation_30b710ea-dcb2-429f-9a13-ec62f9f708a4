{"name": "coffeescript", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin atom/language-coffee-script grammars/coffeescript.cson ./syntaxes/coffeescript.tmLanguage.json"}, "contributes": {"languages": [{"id": "coffeescript", "extensions": [".coffee", ".cson", ".iced"], "aliases": ["CoffeeScript", "coffeescript", "coffee"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "coffeescript", "scopeName": "source.coffee", "path": "./syntaxes/coffeescript.tmLanguage.json"}], "breakpoints": [{"language": "coffeescript"}], "snippets": [{"language": "coffeescript", "path": "./snippets/coffeescript.code-snippets"}], "configurationDefaults": {"[coffeescript]": {"diffEditor.ignoreTrimWhitespace": false}}}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}