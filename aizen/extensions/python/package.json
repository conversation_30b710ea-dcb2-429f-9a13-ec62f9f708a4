{"name": "python", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "contributes": {"languages": [{"id": "python", "extensions": [".py", ".rpy", ".pyw", ".cpy", ".gyp", ".gypi", ".pyi", ".ipy", ".pyt"], "aliases": ["Python", "py"], "filenames": ["SConstruct", "SConscript"], "firstLine": "^#!\\s*/?.*\\bpython[0-9.-]*\\b", "configuration": "./language-configuration.json"}], "grammars": [{"language": "python", "scopeName": "source.python", "path": "./syntaxes/MagicPython.tmLanguage.json"}, {"scopeName": "source.regexp.python", "path": "./syntaxes/MagicRegExp.tmLanguage.json"}], "configurationDefaults": {"[python]": {"diffEditor.ignoreTrimWhitespace": false}}}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin MagicStack/MagicPython grammars/MagicPython.tmLanguage ./syntaxes/MagicPython.tmLanguage.json grammars/MagicRegExp.tmLanguage ./syntaxes/MagicRegExp.tmLanguage.json"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}