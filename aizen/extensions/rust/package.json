{"name": "rust", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin dustypomerleau/rust-syntax syntaxes/rust.tmLanguage.json ./syntaxes/rust.tmLanguage.json"}, "contributes": {"languages": [{"id": "rust", "extensions": [".rs"], "aliases": ["Rust", "rust"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "rust", "path": "./syntaxes/rust.tmLanguage.json", "scopeName": "source.rust"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}