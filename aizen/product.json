{"nameShort": "<PERSON><PERSON>", "nameLong": "Aizen Code Editor", "applicationName": "aizen", "dataFolderName": ".aizen", "win32MutexName": "aizen", "licenseName": "MIT", "licenseUrl": "https://github.com/OGbikram0001/aizen/blob/main/LICENSE.txt", "serverLicenseUrl": "https://github.com/OGbikram0001/aizen/blob/main/LICENSE.txt", "serverGreeting": [], "serverLicense": [], "serverLicensePrompt": "", "serverApplicationName": "aizen-server", "serverDataFolderName": ".aizen-server", "tunnelApplicationName": "aizen-tunnel", "win32DirName": "<PERSON><PERSON>", "win32NameVersion": "<PERSON><PERSON>", "win32RegValueName": "<PERSON><PERSON>", "win32AppId": "{{E34003BB-9E10-4501-8C11-BE3FAA83F23F}", "win32x64AppId": "{{D77B7E06-80BA-4137-BCF4-654B95CCEBC5}", "win32arm64AppId": "{{D1ACE434-89C5-48D1-88D3-E2991DF85475}", "win32UserAppId": "{{C6065F05-9603-4FC4-8101-B9781A25D88E}", "win32x64UserAppId": "{{CC6B787D-37A0-49E8-AE24-8559A032BE0C}", "win32arm64UserAppId": "{{3AEBF0C8-F733-4AD4-BADE-FDB816D53D7B}", "win32AppUserModelId": "Aizen.CodeEditor", "win32ShellNameShort": "&Aizen", "win32TunnelServiceMutex": "aizen-tunnelservice", "win32TunnelMutex": "aizen-tunnel", "darwinBundleIdentifier": "com.aizen.editor", "linuxIconName": "aizen", "licenseFileName": "LICENSE.txt", "reportIssueUrl": "https://github.com/OGbikram0001/aizen/issues/new", "nodejsRepository": "https://nodejs.org", "urlProtocol": "aizen", "webviewContentExternalBaseUrlTemplate": "https://{{uuid}}.vscode-cdn.net/insider/ef65ac1ba57f57f2a3961bfe94aa20481caca4c6/out/vs/workbench/contrib/webview/browser/pre/", "extensionsGallery": {"serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery", "searchUrl": "https://vscode.blob.core.windows.net/gallery/index", "itemUrl": "https://marketplace.visualstudio.com/items", "publisherUrl": "https://marketplace.visualstudio.com/publishers", "resourceUrlTemplate": "https://{publisher}.vscode-unpkg.net/{publisher}/{name}/{version}/{path}", "controlUrl": "https://az764295.vo.msecnd.net/extensions/marketplace.json", "nlsBaseUrl": "https://www.vscode-unpkg.net/_locales"}, "builtInExtensions": [{"name": "ms-vscode.js-debug-companion", "version": "1.1.2", "sha256": "e034b8b41beb4e97e02c70f7175bd88abe66048374c2bd629f54bb33354bc2aa", "repo": "https://github.com/microsoft/vscode-js-debug-companion", "metadata": {"id": "99cb0b7f-7354-4278-b8da-6cc79972169d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}, {"name": "ms-vscode.js-debug", "version": "1.83.0", "sha256": "8e81c3ba8e3b643c54f4dccc0b9402ea605c2bee57758bdfdda61501ea8a23d9", "repo": "https://github.com/microsoft/vscode-js-debug", "metadata": {"id": "25629058-ddac-4e17-abba-74678e126c5d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}, {"name": "ms-vscode.vscode-js-profile-table", "version": "1.0.3", "sha256": "b9dab017506d9e6a469a0f82b392e4cb1d7a25a4843f1db8ba396cbee209cfc5", "repo": "https://github.com/microsoft/vscode-js-profile-visualizer", "metadata": {"id": "7e52b41b-71ad-457b-ab7e-0620f1fc4feb", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}]}