{"name": "code-oss-dev-smoke-test", "version": "0.1.0", "license": "MIT", "main": "./src/main.js", "scripts": {"compile": "yarn --cwd ../automation compile && node ../../node_modules/typescript/bin/tsc", "watch-automation": "yarn --cwd ../automation watch", "watch-smoke": "node ../../node_modules/typescript/bin/tsc --watch --preserveWatchOutput", "watch": "npm-run-all -lp watch-automation watch-smoke", "mocha": "node ../node_modules/mocha/bin/mocha"}, "dependencies": {"@vscode/test-electron": "^2.3.2", "mkdirp": "^1.0.4", "ncp": "^2.0.0", "node-fetch": "^2.6.7", "rimraf": "3.0.2"}, "devDependencies": {"@types/mkdirp": "^1.0.1", "@types/mocha": "^9.1.1", "@types/ncp": "2.0.1", "@types/node": "18.x", "@types/node-fetch": "^2.5.10", "@types/rimraf": "3.0.2", "npm-run-all": "^4.1.5", "watch": "^1.0.2"}}