<html>

<head>
	<meta charset="utf-8">
	<title>VSCode Tests</title>
	<link href="../../../node_modules/mocha/mocha.css" rel="stylesheet" />
</head>

<body>
	<div id="mocha"></div>
	<script src="../../../node_modules/mocha/mocha.js"></script>
	<script>
		// !!! DO NOT CHANGE !!!
		// Our unit tests may run in environments without
		// display (e.g. from builds) and tests may by
		// accident bring up native dialogs or even open
		// windows. This we cannot allow as it may crash
		// the test run.
		// !!! DO NOT CHANGE !!!
		window.open = function () { throw new Error('window.open() is not supported in tests!'); };
		window.alert = function () { throw new Error('window.alert() is not supported in tests!'); }
		window.confirm = function () { throw new Error('window.confirm() is not supported in tests!'); }

		// Ignore uncaught cancelled promise errors
		window.addEventListener('unhandledrejection', e => {
			const name = e && e.reason && e.reason.name;

			if (name === 'Canceled') {
				e.preventDefault();
				e.stopPropagation();
			}
		});

		const urlParams = new URLSearchParams(window.location.search);
		const isCI = urlParams.get('ci');

		mocha.setup({
			ui: 'tdd',
			timeout: isCI ? 30000 : 5000
		});
	</script>

	<!-- Depending on --build or not, load loader from known locations -->
	<script src="../../../out/vs/loader.js"></script>
	<script src="../../../out-build/vs/loader.js"></script>

	<script>
		const isBuild = urlParams.get('build');

		// configure loader
		const baseUrl = window.location.href;
		require.config({
			catchError: true,
			baseUrl: urlParams.get('baseUrl'),
			paths: {
				vs: new URL(`../../../${!!isBuild ? 'out-build' : 'out'}/vs`, baseUrl).href,
				assert: new URL('../assert.js', baseUrl).href,
				sinon: new URL('../../../node_modules/sinon/pkg/sinon.js', baseUrl).href,
				'sinon-test': new URL('../../../node_modules/sinon-test/dist/sinon-test.js', baseUrl).href,
				xterm: new URL('../../../node_modules/xterm/lib/xterm.js', baseUrl).href,
				'@vscode/iconv-lite-umd': new URL('../../../node_modules/@vscode/iconv-lite-umd/lib/iconv-lite-umd.js', baseUrl).href,
				jschardet: new URL('../../../node_modules/jschardet/dist/jschardet.min.js', baseUrl).href
			}
		});
	</script>

	<script>
		function serializeSuite(suite) {
			return {
				root: suite.root,
				suites: suite.suites.map(serializeSuite),
				tests: suite.tests.map(serializeRunnable),
				title: suite.title,
				fullTitle: suite.fullTitle(),
				titlePath: suite.titlePath(),
				timeout: suite.timeout(),
				retries: suite.retries(),
				slow: suite.slow(),
				bail: suite.bail()
			};
		}
		function serializeRunnable(runnable) {
			return {
				title: runnable.title,
				titlePath: runnable.titlePath(),
				fullTitle: runnable.fullTitle(),
				async: runnable.async,
				slow: runnable.slow(),
				speed: runnable.speed,
				duration: runnable.duration,
				currentRetry: runnable.currentRetry(),
			};
		}
		function serializeError(err) {
			return {
				message: err.message,
				stack: err.stack,
				actual: err.actual,
				expected: err.expected,
				uncaught: err.uncaught,
				showDiff: err.showDiff,
				inspect: typeof err.inspect === 'function' ? err.inspect() : ''
			};
		}
		function PlaywrightReporter(runner) {
			runner.on('start', () => window.mocha_report('start'));
			runner.on('end', () => window.mocha_report('end'));
			runner.on('suite', suite => window.mocha_report('suite', serializeSuite(suite)));
			runner.on('suite end', suite => window.mocha_report('suite end', serializeSuite(suite)));
			runner.on('test', test => window.mocha_report('test', serializeRunnable(test)));
			runner.on('test end', test => window.mocha_report('test end', serializeRunnable(test)));
			runner.on('hook', hook => window.mocha_report('hook', serializeRunnable(hook)));
			runner.on('hook end', hook => window.mocha_report('hook end', serializeRunnable(hook)));
			runner.on('pass', test => window.mocha_report('pass', serializeRunnable(test)));
			runner.on('fail', (test, err) => window.mocha_report('fail', serializeRunnable(test), serializeError(err)));
			runner.on('pending', test => window.mocha_report('pending', serializeRunnable(test)));
		};

		const remoteMethods = [
			'__readFileInTests',
			'__writeFileInTests',
			'__readDirInTests',
			'__unlinkInTests',
			'__mkdirPInTests',
		];

		for (const method of remoteMethods) {
			const prefix = window.location.pathname.split('/')[1];
			globalThis[method] = async (...args) => {
				const res = await fetch(`/${prefix}/remoteMethod/${method}`, {
					body: JSON.stringify(args),
					method: 'POST',
					headers: { 'Content-Type': 'application/json' }
				});

				return res.json();
			}
		}


		async function loadModules(modules) {
			for (const file of modules) {
				mocha.suite.emit(Mocha.Suite.constants.EVENT_FILE_PRE_REQUIRE, globalThis, file, mocha);
				const m = await new Promise((resolve, reject) => require([file], resolve, err => {
					console.log("BAD " + file + JSON.stringify(err, undefined, '\t'));
					resolve({});
				}));
				mocha.suite.emit(Mocha.Suite.constants.EVENT_FILE_REQUIRE, m, file, mocha);
				mocha.suite.emit(Mocha.Suite.constants.EVENT_FILE_POST_REQUIRE, globalThis, file, mocha);
			}
		}

		window.loadAndRun = async function loadAndRun({ modules, grep }, manual = false) {
			// load
			await loadModules(modules);
			// await new Promise((resolve, reject) => {
			// 	require(modules, resolve, err => {
			// 		console.log(err);
			// 		reject(err);
			// 	});
			// });

			// run
			return new Promise((resolve, reject) => {
				if (grep) {
					mocha.grep(grep);
				}

				if (!manual) {
					mocha.reporter(PlaywrightReporter);
				}
				mocha.run(failCount => resolve(failCount === 0));
			});
		}

		const url = new URL(window.location.href);
		const modules = url.searchParams.getAll('m');
		if (Array.isArray(modules) && modules.length > 0) {
			console.log('MANUALLY running tests', modules);

			loadAndRun({modules}, true).then(() => console.log('done'), err => console.log(err));
		}
	</script>
</body>

</html>
