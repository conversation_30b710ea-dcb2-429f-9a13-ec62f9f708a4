{
	"compilerOptions": {
		"noEmit": true,
		"module": "None",
		"experimentalDecorators": false,
		"noImplicitReturns": true,
		"noImplicitOverride": true,
		"noUnusedLocals": true,
		"allowUnreachableCode": false,
		"strict": true,
		"exactOptionalPropertyTypes": false,
		"useUnknownInCatchVariables": false,
		"forceConsistentCasingInFileNames": true,
		"types": [],
		"lib": [
			"es5",
			"ES2015.Iterable"
		],
	},
	"include": [
		"vscode-dts/vscode.d.ts"
	]
}
