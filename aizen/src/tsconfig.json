{"extends": "./tsconfig.base.json", "compilerOptions": {"removeComments": false, "preserveConstEnums": true, "sourceMap": false, "allowJs": true, "resolveJsonModule": true, "outDir": "../out/vs", "types": ["mocha", "semver", "sinon", "winreg", "trusted-types", "wicg-file-system-access"], "plugins": [{"name": "tsec", "exemptionConfig": "./tsec.exemptions.json"}]}, "include": ["./bootstrap.js", "./bootstrap-amd.js", "./server-main.js", "./typings", "./vs/**/*.ts", "vscode-dts/vscode.proposed.*.d.ts", "vscode-dts/vscode.d.ts"]}