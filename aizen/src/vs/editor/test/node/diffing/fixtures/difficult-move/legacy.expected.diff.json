{"original": {"content": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n'use strict';\n\nconst gulp = require('gulp');\nconst path = require('path');\nconst es = require('event-stream');\nconst util = require('./lib/util');\nconst { getVersion } = require('./lib/getVersion');\nconst task = require('./lib/task');\nconst optimize = require('./lib/optimize');\nconst product = require('../product.json');\nconst rename = require('gulp-rename');\nconst replace = require('gulp-replace');\nconst filter = require('gulp-filter');\nconst { getProductionDependencies } = require('./lib/dependencies');\nconst vfs = require('vinyl-fs');\nconst packageJson = require('../package.json');\nconst flatmap = require('gulp-flatmap');\nconst gunzip = require('gulp-gunzip');\nconst File = require('vinyl');\nconst fs = require('fs');\nconst glob = require('glob');\nconst { compileBuildTask } = require('./gulpfile.compile');\nconst { compileExtensionsBuildTask, compileExtensionMediaBuildTask } = require('./gulpfile.extensions');\nconst { vscodeWebEntryPoints, vscodeWebResourceIncludes, createVSCodeWebFileContentMapper } = require('./gulpfile.vscode.web');\nconst cp = require('child_process');\nconst log = require('fancy-log');\n\nconst REPO_ROOT = path.dirname(__dirname);\nconst commit = getVersion(REPO_ROOT);\nconst BUILD_ROOT = path.dirname(REPO_ROOT);\nconst REMOTE_FOLDER = path.join(REPO_ROOT, 'remote');\n\n// Targets\n\nconst BUILD_TARGETS = [\n\t{ platform: 'win32', arch: 'ia32' },\n\t{ platform: 'win32', arch: 'x64' },\n\t{ platform: 'darwin', arch: 'x64' },\n\t{ platform: 'darwin', arch: 'arm64' },\n\t{ platform: 'linux', arch: 'x64' },\n\t{ platform: 'linux', arch: 'armhf' },\n\t{ platform: 'linux', arch: 'arm64' },\n\t{ platform: 'alpine', arch: 'arm64' },\n\t// legacy: we use to ship only one alpine so it was put in the arch, but now we ship\n\t// multiple alpine images and moved to a better model (alpine as the platform)\n\t{ platform: 'linux', arch: 'alpine' },\n];\n\nconst serverResources = [\n\n\t// Bootstrap\n\t'out-build/bootstrap.js',\n\t'out-build/bootstrap-fork.js',\n\t'out-build/bootstrap-amd.js',\n\t'out-build/bootstrap-node.js',\n\n\t// Performance\n\t'out-build/vs/base/common/performance.js',\n\n\t// Watcher\n\t'out-build/vs/platform/files/**/*.exe',\n\t'out-build/vs/platform/files/**/*.md',\n\n\t// Process monitor\n\t'out-build/vs/base/node/cpuUsage.sh',\n\t'out-build/vs/base/node/ps.sh',\n\n\t// Terminal shell integration\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration.ps1',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-bash.sh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-env.zsh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-profile.zsh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-rc.zsh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-login.zsh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/fish_xdg_data/fish/vendor_conf.d/shellIntegration.fish',\n\n\t'!**/test/**'\n];\n\nconst serverWithWebResources = [\n\n\t// Include all of server...\n\t...serverResources,\n\n\t// ...and all of web\n\t...vscodeWebResourceIncludes\n];\n\nconst serverEntryPoints = [\n\t{\n\t\tname: 'vs/server/node/server.main',\n\t\texclude: ['vs/css', 'vs/nls']\n\t},\n\t{\n\t\tname: 'vs/server/node/server.cli',\n\t\texclude: ['vs/css', 'vs/nls']\n\t},\n\t{\n\t\tname: 'vs/workbench/api/node/extensionHostProcess',\n\t\texclude: ['vs/css', 'vs/nls']\n\t},\n\t{\n\t\tname: 'vs/platform/files/node/watcher/watcherMain',\n\t\texclude: ['vs/css', 'vs/nls']\n\t},\n\t{\n\t\tname: 'vs/platform/terminal/node/ptyHostMain',\n\t\texclude: ['vs/css', 'vs/nls']\n\t}\n];\n\nconst serverWithWebEntryPoints = [\n\n\t// Include all of server\n\t...serverEntryPoints,\n\n\t// Include workbench web\n\t...vscodeWebEntryPoints\n];\n\nfunction getNodeVersion() {\n\tconst yarnrc = fs.readFileSync(path.join(REPO_ROOT, 'remote', '.yarnrc'), 'utf8');\n\tconst nodeVersion = /^target \"(.*)\"$/m.exec(yarnrc)[1];\n\tconst internalNodeVersion = /^ms_build_id \"(.*)\"$/m.exec(yarnrc)[1];\n\treturn { nodeVersion, internalNodeVersion };\n}\n\nfunction getNodeChecksum(nodeVersion, platform, arch) {\n\tlet expectedName;\n\tswitch (platform) {\n\t\tcase 'win32':\n\t\t\texpectedName = `win-${arch}/node.exe`;\n\t\t\tbreak;\n\n\t\tcase 'darwin':\n\t\tcase 'linux':\n\t\t\texpectedName = `node-v${nodeVersion}-${platform}-${arch}.tar.gz`;\n\t\t\tbreak;\n\n\t\tcase 'alpine':\n\t\t\texpectedName = `${platform}-${arch}/node`;\n\t\t\tbreak;\n\t}\n\n\tconst nodeJsChecksums = fs.readFileSync(path.join(REPO_ROOT, 'build', 'checksums', 'nodejs.txt'), 'utf8');\n\tfor (const line of nodeJsChecksums.split('\\n')) {\n\t\tconst [checksum, name] = line.split(/\\s+/);\n\t\tif (name === expectedName) {\n\t\t\treturn checksum;\n\t\t}\n\t}\n\treturn undefined;\n}\n\nconst { nodeVersion, internalNodeVersion } = getNodeVersion();\n\nBUILD_TARGETS.forEach(({ platform, arch }) => {\n\tgulp.task(task.define(`node-${platform}-${arch}`, () => {\n\t\tconst nodePath = path.join('.build', 'node', `v${nodeVersion}`, `${platform}-${arch}`);\n\n\t\tif (!fs.existsSync(nodePath)) {\n\t\t\tutil.rimraf(nodePath);\n\n\t\t\treturn nodejs(platform, arch)\n\t\t\t\t.pipe(vfs.dest(nodePath));\n\t\t}\n\n\t\treturn Promise.resolve(null);\n\t}));\n});\n\nconst defaultNodeTask = gulp.task(`node-${process.platform}-${process.arch}`);\n\nif (defaultNodeTask) {\n\tgulp.task(task.define('node', defaultNodeTask));\n}\n\nfunction nodejs(platform, arch) {\n\tconst { fetchUrls, fetchGithub } = require('./lib/fetch');\n\tconst untar = require('gulp-untar');\n\tconst crypto = require('crypto');\n\n\tif (arch === 'ia32') {\n\t\tarch = 'x86';\n\t} else if (arch === 'armhf') {\n\t\tarch = 'armv7l';\n\t} else if (arch === 'alpine') {\n\t\tplatform = 'alpine';\n\t\tarch = 'x64';\n\t}\n\n\tlog(`Downloading node.js ${nodeVersion} ${platform} ${arch} from ${product.nodejsRepository}...`);\n\n\tconst checksumSha256 = getNodeChecksum(nodeVersion, platform, arch);\n\n\tif (checksumSha256) {\n\t\tlog(`Using SHA256 checksum for checking integrity: ${checksumSha256}`);\n\t} else {\n\t\tlog.warn(`Unable to verify integrity of downloaded node.js binary because no SHA256 checksum was found!`);\n\t}\n\n\tswitch (platform) {\n\t\tcase 'win32':\n\t\t\treturn (product.nodejsRepository !== 'https://nodejs.org' ?\n\t\t\t\tfetchGithub(product.nodejsRepository, { version: `${nodeVersion}-${internalNodeVersion}`, name: `win-${arch}-node.exe`, checksumSha256 }) :\n\t\t\t\tfetchUrls(`/dist/v${nodeVersion}/win-${arch}/node.exe`, { base: 'https://nodejs.org', checksumSha256 }))\n\t\t\t\t.pipe(rename('node.exe'));\n\t\tcase 'darwin':\n\t\tcase 'linux':\n\t\t\treturn (product.nodejsRepository !== 'https://nodejs.org' ?\n\t\t\t\tfetchGithub(product.nodejsRepository, { version: `${nodeVersion}-${internalNodeVersion}`, name: `node-v${nodeVersion}-${platform}-${arch}.tar.gz`, checksumSha256 }) :\n\t\t\t\tfetchUrls(`/dist/v${nodeVersion}/node-v${nodeVersion}-${platform}-${arch}.tar.gz`, { base: 'https://nodejs.org', checksumSha256 })\n\t\t\t).pipe(flatmap(stream => stream.pipe(gunzip()).pipe(untar())))\n\t\t\t\t.pipe(filter('**/node'))\n\t\t\t\t.pipe(util.setExecutableBit('**'))\n\t\t\t\t.pipe(rename('node'));\n\t\tcase 'alpine': {\n\t\t\tconst imageName = arch === 'arm64' ? 'arm64v8/node' : 'node';\n\t\t\tlog(`Downloading node.js ${nodeVersion} ${platform} ${arch} from docker image ${imageName}`);\n\t\t\tconst contents = cp.execSync(`docker run --rm ${imageName}:${nodeVersion}-alpine /bin/sh -c 'cat \\`which node\\`'`, { maxBuffer: 100 * 1024 * 1024, encoding: 'buffer' });\n\t\t\tif (checksumSha256) {\n\t\t\t\tconst actualSHA256Checksum = crypto.createHash('sha256').update(contents).digest('hex');\n\t\t\t\tif (actualSHA256Checksum !== checksumSha256) {\n\t\t\t\t\tthrow new Error(`Checksum mismatch for node.js from docker image (expected ${options.checksumSha256}, actual ${actualSHA256Checksum}))`);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn es.readArray([new File({ path: 'node', contents, stat: { mode: parseInt('755', 8) } })]);\n\t\t}\n\t}\n}\n\nfunction packageTask(type, platform, arch, sourceFolderName, destinationFolderName) {\n\tconst destination = path.join(BUILD_ROOT, destinationFolderName);\n\n\treturn () => {\n\t\tconst json = require('gulp-json-editor');\n\n\t\tconst src = gulp.src(sourceFolderName + '/**', { base: '.' })\n\t\t\t.pipe(rename(function (path) { path.dirname = path.dirname.replace(new RegExp('^' + sourceFolderName), 'out'); }))\n\t\t\t.pipe(util.setExecutableBit(['**/*.sh']))\n\t\t\t.pipe(filter(['**', '!**/*.js.map']));\n\n\t\tconst workspaceExtensionPoints = ['debuggers', 'jsonValidation'];\n\t\tconst isUIExtension = (manifest) => {\n\t\t\tswitch (manifest.extensionKind) {\n\t\t\t\tcase 'ui': return true;\n\t\t\t\tcase 'workspace': return false;\n\t\t\t\tdefault: {\n\t\t\t\t\tif (manifest.main) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif (manifest.contributes && Object.keys(manifest.contributes).some(key => workspaceExtensionPoints.indexOf(key) !== -1)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\t// Default is UI Extension\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\tconst localWorkspaceExtensions = glob.sync('extensions/*/package.json')\n\t\t\t.filter((extensionPath) => {\n\t\t\t\tif (type === 'reh-web') {\n\t\t\t\t\treturn true; // web: ship all extensions for now\n\t\t\t\t}\n\n\t\t\t\t// Skip shipping UI extensions because the client side will have them anyways\n\t\t\t\t// and they'd just increase the download without being used\n\t\t\t\tconst manifest = JSON.parse(fs.readFileSync(path.join(REPO_ROOT, extensionPath)).toString());\n\t\t\t\treturn !isUIExtension(manifest);\n\t\t\t}).map((extensionPath) => path.basename(path.dirname(extensionPath)))\n\t\t\t.filter(name => name !== 'vscode-api-tests' && name !== 'vscode-test-resolver'); // Do not ship the test extensions\n\t\tconst marketplaceExtensions = JSON.parse(fs.readFileSync(path.join(REPO_ROOT, 'product.json'), 'utf8')).builtInExtensions\n\t\t\t.filter(entry => !entry.platforms || new Set(entry.platforms).has(platform))\n\t\t\t.filter(entry => !entry.clientOnly)\n\t\t\t.map(entry => entry.name);\n\t\tconst extensionPaths = [...localWorkspaceExtensions, ...marketplaceExtensions]\n\t\t\t.map(name => `.build/extensions/${name}/**`);\n\n\t\tconst extensions = gulp.src(extensionPaths, { base: '.build', dot: true });\n\t\tconst extensionsCommonDependencies = gulp.src('.build/extensions/node_modules/**', { base: '.build', dot: true });\n\t\tconst sources = es.merge(src, extensions, extensionsCommonDependencies)\n\t\t\t.pipe(filter(['**', '!**/*.js.map'], { dot: true }));\n\n\t\tlet version = packageJson.version;\n\t\tconst quality = product.quality;\n\n\t\tif (quality && quality !== 'stable') {\n\t\t\tversion += '-' + quality;\n\t\t}\n\n\t\tconst name = product.nameShort;\n\t\tconst packageJsonStream = gulp.src(['remote/package.json'], { base: 'remote' })\n\t\t\t.pipe(json({ name, version, dependencies: undefined, optionalDependencies: undefined }));\n\n\t\tconst date = new Date().toISOString();\n\n\t\tconst productJsonStream = gulp.src(['product.json'], { base: '.' })\n\t\t\t.pipe(json({ commit, date, version }));\n\n\t\tconst license = gulp.src(['remote/LICENSE'], { base: 'remote', allowEmpty: true });\n\n\t\tconst jsFilter = util.filter(data => !data.isDirectory() && /\\.js$/.test(data.path));\n\n\t\tconst productionDependencies = getProductionDependencies(REMOTE_FOLDER);\n\t\tconst dependenciesSrc = productionDependencies.map(d => path.relative(REPO_ROOT, d.path)).map(d => [`${d}/**`, `!${d}/**/{test,tests}/**`, `!${d}/.bin/**`]).flat();\n\t\tconst deps = gulp.src(dependenciesSrc, { base: 'remote', dot: true })\n\t\t\t// filter out unnecessary files, no source maps in server build\n\t\t\t.pipe(filter(['**', '!**/package-lock.json', '!**/yarn.lock', '!**/*.js.map']))\n\t\t\t.pipe(util.cleanNodeModules(path.join(__dirname, '.moduleignore')))\n\t\t\t.pipe(util.cleanNodeModules(path.join(__dirname, `.moduleignore.${process.platform}`)))\n\t\t\t.pipe(jsFilter)\n\t\t\t.pipe(util.stripSourceMappingURL())\n\t\t\t.pipe(jsFilter.restore);\n\n\t\tconst nodePath = `.build/node/v${nodeVersion}/${platform}-${arch}`;\n\t\tconst node = gulp.src(`${nodePath}/**`, { base: nodePath, dot: true });\n\n\t\tlet web = [];\n\t\tif (type === 'reh-web') {\n\t\t\tweb = [\n\t\t\t\t'resources/server/favicon.ico',\n\t\t\t\t'resources/server/code-192.png',\n\t\t\t\t'resources/server/code-512.png',\n\t\t\t\t'resources/server/manifest.json'\n\t\t\t].map(resource => gulp.src(resource, { base: '.' }).pipe(rename(resource)));\n\t\t}\n\n\t\tconst all = es.merge(\n\t\t\tpackageJsonStream,\n\t\t\tproductJsonStream,\n\t\t\tlicense,\n\t\t\tsources,\n\t\t\tdeps,\n\t\t\tnode,\n\t\t\t...web\n\t\t);\n\n\t\tlet result = all\n\t\t\t.pipe(util.skipDirectories())\n\t\t\t.pipe(util.fixWin32DirectoryPermissions());\n\n\t\tif (platform === 'win32') {\n\t\t\tresult = es.merge(result,\n\t\t\t\tgulp.src('resources/server/bin/remote-cli/code.cmd', { base: '.' })\n\t\t\t\t\t.pipe(replace('@@VERSION@@', version))\n\t\t\t\t\t.pipe(replace('@@COMMIT@@', commit))\n\t\t\t\t\t.pipe(replace('@@APPNAME@@', product.applicationName))\n\t\t\t\t\t.pipe(rename(`bin/remote-cli/${product.applicationName}.cmd`)),\n\t\t\t\tgulp.src('resources/server/bin/helpers/browser.cmd', { base: '.' })\n\t\t\t\t\t.pipe(replace('@@VERSION@@', version))\n\t\t\t\t\t.pipe(replace('@@COMMIT@@', commit))\n\t\t\t\t\t.pipe(replace('@@APPNAME@@', product.applicationName))\n\t\t\t\t\t.pipe(rename(`bin/helpers/browser.cmd`)),\n\t\t\t\tgulp.src('resources/server/bin/code-server.cmd', { base: '.' })\n\t\t\t\t\t.pipe(rename(`bin/${product.serverApplicationName}.cmd`)),\n\t\t\t);\n\t\t} else if (platform === 'linux' || platform === 'alpine' || platform === 'darwin') {\n\t\t\tresult = es.merge(result,\n\t\t\t\tgulp.src(`resources/server/bin/remote-cli/${platform === 'darwin' ? 'code-darwin.sh' : 'code-linux.sh'}`, { base: '.' })\n\t\t\t\t\t.pipe(replace('@@VERSION@@', version))\n\t\t\t\t\t.pipe(replace('@@COMMIT@@', commit))\n\t\t\t\t\t.pipe(replace('@@APPNAME@@', product.applicationName))\n\t\t\t\t\t.pipe(rename(`bin/remote-cli/${product.applicationName}`))\n\t\t\t\t\t.pipe(util.setExecutableBit()),\n\t\t\t\tgulp.src(`resources/server/bin/helpers/${platform === 'darwin' ? 'browser-darwin.sh' : 'browser-linux.sh'}`, { base: '.' })\n\t\t\t\t\t.pipe(replace('@@VERSION@@', version))\n\t\t\t\t\t.pipe(replace('@@COMMIT@@', commit))\n\t\t\t\t\t.pipe(replace('@@APPNAME@@', product.applicationName))\n\t\t\t\t\t.pipe(rename(`bin/helpers/browser.sh`))\n\t\t\t\t\t.pipe(util.setExecutableBit()),\n\t\t\t\tgulp.src(`resources/server/bin/${platform === 'darwin' ? 'code-server-darwin.sh' : 'code-server-linux.sh'}`, { base: '.' })\n\t\t\t\t\t.pipe(rename(`bin/${product.serverApplicationName}`))\n\t\t\t\t\t.pipe(util.setExecutableBit())\n\t\t\t);\n\t\t}\n\n\t\treturn result.pipe(vfs.dest(destination));\n\t};\n}\n\n/**\n * @param {object} product The parsed product.json file contents\n */\nfunction tweakProductForServerWeb(product) {\n\tconst result = { ...product };\n\tdelete result.webEndpointUrlTemplate;\n\treturn result;\n}\n\n['reh', 'reh-web'].forEach(type => {\n\tconst optimizeTask = task.define(`optimize-vscode-${type}`, task.series(\n\t\tutil.rimraf(`out-vscode-${type}`),\n\t\toptimize.optimizeTask(\n\t\t\t{\n\t\t\t\tout: `out-vscode-${type}`,\n\t\t\t\tamd: {\n\t\t\t\t\tsrc: 'out-build',\n\t\t\t\t\tentryPoints: (type === 'reh' ? serverEntryPoints : serverWithWebEntryPoints).flat(),\n\t\t\t\t\totherSources: [],\n\t\t\t\t\tresources: type === 'reh' ? serverResources : serverWithWebResources,\n\t\t\t\t\tloaderConfig: optimize.loaderConfig(),\n\t\t\t\t\tinlineAmdImages: true,\n\t\t\t\t\tbundleInfo: undefined,\n\t\t\t\t\tfileContentMapper: createVSCodeWebFileContentMapper('.build/extensions', type === 'reh-web' ? tweakProductForServerWeb(product) : product)\n\t\t\t\t},\n\t\t\t\tcommonJS: {\n\t\t\t\t\tsrc: 'out-build',\n\t\t\t\t\tentryPoints: [\n\t\t\t\t\t\t'out-build/server-main.js',\n\t\t\t\t\t\t'out-build/server-cli.js'\n\t\t\t\t\t],\n\t\t\t\t\tplatform: 'node',\n\t\t\t\t\texternal: [\n\t\t\t\t\t\t'minimist',\n\t\t\t\t\t\t// TODO: we cannot inline `product.json` because\n\t\t\t\t\t\t// it is being changed during build time at a later\n\t\t\t\t\t\t// point in time (such as `checksums`)\n\t\t\t\t\t\t'../product.json',\n\t\t\t\t\t\t'../package.json'\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t}\n\t\t)\n\t));\n\n\tconst minifyTask = task.define(`minify-vscode-${type}`, task.series(\n\t\toptimizeTask,\n\t\tutil.rimraf(`out-vscode-${type}-min`),\n\t\toptimize.minifyTask(`out-vscode-${type}`, `https://ticino.blob.core.windows.net/sourcemaps/${commit}/core`)\n\t));\n\tgulp.task(minifyTask);\n\n\tBUILD_TARGETS.forEach(buildTarget => {\n\t\tconst dashed = (str) => (str ? `-${str}` : ``);\n\t\tconst platform = buildTarget.platform;\n\t\tconst arch = buildTarget.arch;\n\n\t\t['', 'min'].forEach(minified => {\n\t\t\tconst sourceFolderName = `out-vscode-${type}${dashed(minified)}`;\n\t\t\tconst destinationFolderName = `vscode-${type}${dashed(platform)}${dashed(arch)}`;\n\n\t\t\tconst serverTaskCI = task.define(`vscode-${type}${dashed(platform)}${dashed(arch)}${dashed(minified)}-ci`, task.series(\n\t\t\t\tgulp.task(`node-${platform}-${arch}`),\n\t\t\t\tutil.rimraf(path.join(BUILD_ROOT, destinationFolderName)),\n\t\t\t\tpackageTask(type, platform, arch, sourceFolderName, destinationFolderName)\n\t\t\t));\n\t\t\tgulp.task(serverTaskCI);\n\n\t\t\tconst serverTask = task.define(`vscode-${type}${dashed(platform)}${dashed(arch)}${dashed(minified)}`, task.series(\n\t\t\t\tcompileBuildTask,\n\t\t\t\tcompileExtensionsBuildTask,\n\t\t\t\tcompileExtensionMediaBuildTask,\n\t\t\t\tminified ? minifyTask : optimizeTask,\n\t\t\t\tserverTaskCI\n\t\t\t));\n\t\t\tgulp.task(serverTask);\n\t\t});\n\t});\n});\n", "fileName": "./1.js"}, "modified": {"content": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n'use strict';\n\nconst gulp = require('gulp');\nconst path = require('path');\nconst es = require('event-stream');\nconst util = require('./lib/util');\nconst { getVersion } = require('./lib/getVersion');\nconst task = require('./lib/task');\nconst optimize = require('./lib/optimize');\nconst product = require('../product.json');\nconst rename = require('gulp-rename');\nconst replace = require('gulp-replace');\nconst filter = require('gulp-filter');\nconst { getProductionDependencies } = require('./lib/dependencies');\nconst vfs = require('vinyl-fs');\nconst packageJson = require('../package.json');\nconst flatmap = require('gulp-flatmap');\nconst gunzip = require('gulp-gunzip');\nconst File = require('vinyl');\nconst fs = require('fs');\nconst glob = require('glob');\nconst { compileBuildTask } = require('./gulpfile.compile');\nconst { compileExtensionsBuildTask, compileExtensionMediaBuildTask } = require('./gulpfile.extensions');\nconst { vscodeWebEntryPoints, vscodeWebResourceIncludes, createVSCodeWebFileContentMapper } = require('./gulpfile.vscode.web');\nconst cp = require('child_process');\nconst log = require('fancy-log');\n\nconst REPO_ROOT = path.dirname(__dirname);\nconst commit = getVersion(REPO_ROOT);\nconst BUILD_ROOT = path.dirname(REPO_ROOT);\nconst REMOTE_FOLDER = path.join(REPO_ROOT, 'remote');\n\n// Targets\n\nconst BUILD_TARGETS = [\n\t{ platform: 'win32', arch: 'ia32' },\n\t{ platform: 'win32', arch: 'x64' },\n\t{ platform: 'darwin', arch: 'x64' },\n\t{ platform: 'darwin', arch: 'arm64' },\n\t{ platform: 'linux', arch: 'x64' },\n\t{ platform: 'linux', arch: 'armhf' },\n\t{ platform: 'linux', arch: 'arm64' },\n\t{ platform: 'alpine', arch: 'arm64' },\n\t// legacy: we use to ship only one alpine so it was put in the arch, but now we ship\n\t// multiple alpine images and moved to a better model (alpine as the platform)\n\t{ platform: 'linux', arch: 'alpine' },\n];\n\nconst serverResources = [\n\n\t// Bootstrap\n\t'out-build/bootstrap.js',\n\t'out-build/bootstrap-fork.js',\n\t'out-build/bootstrap-amd.js',\n\t'out-build/bootstrap-node.js',\n\n\t// Performance\n\t'out-build/vs/base/common/performance.js',\n\n\t// Watcher\n\t'out-build/vs/platform/files/**/*.exe',\n\t'out-build/vs/platform/files/**/*.md',\n\n\t// Process monitor\n\t'out-build/vs/base/node/cpuUsage.sh',\n\t'out-build/vs/base/node/ps.sh',\n\n\t// Terminal shell integration\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration.ps1',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-bash.sh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-env.zsh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-profile.zsh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-rc.zsh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/shellIntegration-login.zsh',\n\t'out-build/vs/workbench/contrib/terminal/browser/media/fish_xdg_data/fish/vendor_conf.d/shellIntegration.fish',\n\n\t'!**/test/**'\n];\n\nconst serverWithWebResources = [\n\n\t// Include all of server...\n\t...serverResources,\n\n\t// ...and all of web\n\t...vscodeWebResourceIncludes\n];\n\nconst serverEntryPoints = [\n\t{\n\t\tname: 'vs/server/node/server.main',\n\t\texclude: ['vs/css', 'vs/nls']\n\t},\n\t{\n\t\tname: 'vs/server/node/server.cli',\n\t\texclude: ['vs/css', 'vs/nls']\n\t},\n\t{\n\t\tname: 'vs/workbench/api/node/extensionHostProcess',\n\t\texclude: ['vs/css', 'vs/nls']\n\t},\n\t{\n\t\tname: 'vs/platform/files/node/watcher/watcherMain',\n\t\texclude: ['vs/css', 'vs/nls']\n\t},\n\t{\n\t\tname: 'vs/platform/terminal/node/ptyHostMain',\n\t\texclude: ['vs/css', 'vs/nls']\n\t}\n];\n\nconst serverWithWebEntryPoints = [\n\n\t// Include all of server\n\t...serverEntryPoints,\n\n\t// Include workbench web\n\t...vscodeWebEntryPoints\n];\n\nfunction getNodeVersion() {\n\tconst yarnrc = fs.readFileSync(path.join(REPO_ROOT, 'remote', '.yarnrc'), 'utf8');\n\tconst nodeVersion = /^target \"(.*)\"$/m.exec(yarnrc)[1];\n\tconst internalNodeVersion = /^ms_build_id \"(.*)\"$/m.exec(yarnrc)[1];\n\treturn { nodeVersion, internalNodeVersion };\n}\n\nfunction getNodeChecksum(nodeVersion, platform, arch) {\n\tlet expectedName;\n\tswitch (platform) {\n\t\tcase 'win32':\n\t\t\texpectedName = `win-${arch}/node.exe`;\n\t\t\tbreak;\n\n\t\tcase 'darwin':\n\t\tcase 'alpine':\n\t\tcase 'linux':\n\t\t\texpectedName = `node-v${nodeVersion}-${platform}-${arch}.tar.gz`;\n\t\t\tbreak;\n\t}\n\n\tconst nodeJsChecksums = fs.readFileSync(path.join(REPO_ROOT, 'build', 'checksums', 'nodejs.txt'), 'utf8');\n\tfor (const line of nodeJsChecksums.split('\\n')) {\n\t\tconst [checksum, name] = line.split(/\\s+/);\n\t\tif (name === expectedName) {\n\t\t\treturn checksum;\n\t\t}\n\t}\n\treturn undefined;\n}\n\nfunction extractAlpinefromDocker(nodeVersion, platform, arch) {\n\tconst imageName = arch === 'arm64' ? 'arm64v8/node' : 'node';\n\tlog(`Downloading node.js ${nodeVersion} ${platform} ${arch} from docker image ${imageName}`);\n\tconst contents = cp.execSync(`docker run --rm ${imageName}:${nodeVersion}-alpine /bin/sh -c 'cat \\`which node\\`'`, { maxBuffer: 100 * 1024 * 1024, encoding: 'buffer' });\n\treturn es.readArray([new File({ path: 'node', contents, stat: { mode: parseInt('755', 8) } })]);\n}\n\nconst { nodeVersion, internalNodeVersion } = getNodeVersion();\n\nBUILD_TARGETS.forEach(({ platform, arch }) => {\n\tgulp.task(task.define(`node-${platform}-${arch}`, () => {\n\t\tconst nodePath = path.join('.build', 'node', `v${nodeVersion}`, `${platform}-${arch}`);\n\n\t\tif (!fs.existsSync(nodePath)) {\n\t\t\tutil.rimraf(nodePath);\n\n\t\t\treturn nodejs(platform, arch)\n\t\t\t\t.pipe(vfs.dest(nodePath));\n\t\t}\n\n\t\treturn Promise.resolve(null);\n\t}));\n});\n\nconst defaultNodeTask = gulp.task(`node-${process.platform}-${process.arch}`);\n\nif (defaultNodeTask) {\n\tgulp.task(task.define('node', defaultNodeTask));\n}\n\nfunction nodejs(platform, arch) {\n\tconst { fetchUrls, fetchGithub } = require('./lib/fetch');\n\tconst untar = require('gulp-untar');\n\tconst crypto = require('crypto');\n\n\tif (arch === 'ia32') {\n\t\tarch = 'x86';\n\t} else if (arch === 'armhf') {\n\t\tarch = 'armv7l';\n\t} else if (arch === 'alpine') {\n\t\tplatform = 'alpine';\n\t\tarch = 'x64';\n\t}\n\n\tlog(`Downloading node.js ${nodeVersion} ${platform} ${arch} from ${product.nodejsRepository}...`);\n\n\tconst checksumSha256 = getNodeChecksum(nodeVersion, platform, arch);\n\n\tif (checksumSha256) {\n\t\tlog(`Using SHA256 checksum for checking integrity: ${checksumSha256}`);\n\t} else {\n\t\tlog.warn(`Unable to verify integrity of downloaded node.js binary because no SHA256 checksum was found!`);\n\t}\n\n\tswitch (platform) {\n\t\tcase 'win32':\n\t\t\treturn (product.nodejsRepository !== 'https://nodejs.org' ?\n\t\t\t\tfetchGithub(product.nodejsRepository, { version: `${nodeVersion}-${internalNodeVersion}`, name: `win-${arch}-node.exe`, checksumSha256 }) :\n\t\t\t\tfetchUrls(`/dist/v${nodeVersion}/win-${arch}/node.exe`, { base: 'https://nodejs.org', checksumSha256 }))\n\t\t\t\t.pipe(rename('node.exe'));\n\t\tcase 'darwin':\n\t\tcase 'linux':\n\t\t\treturn (product.nodejsRepository !== 'https://nodejs.org' ?\n\t\t\t\tfetchGithub(product.nodejsRepository, { version: `${nodeVersion}-${internalNodeVersion}`, name: `node-v${nodeVersion}-${platform}-${arch}.tar.gz`, checksumSha256 }) :\n\t\t\t\tfetchUrls(`/dist/v${nodeVersion}/node-v${nodeVersion}-${platform}-${arch}.tar.gz`, { base: 'https://nodejs.org', checksumSha256 })\n\t\t\t).pipe(flatmap(stream => stream.pipe(gunzip()).pipe(untar())))\n\t\t\t\t.pipe(filter('**/node'))\n\t\t\t\t.pipe(util.setExecutableBit('**'))\n\t\t\t\t.pipe(rename('node'));\n\t\tcase 'alpine':\n\t\t\treturn product.nodejsRepository !== 'https://nodejs.org' ?\n\t\t\t\tfetchGithub(product.nodejsRepository, { version: `${nodeVersion}-${internalNodeVersion}`, name: `node-v${nodeVersion}-${platform}-${arch}.tar.gz`, checksumSha256 })\n\t\t\t\t\t.pipe(flatmap(stream => stream.pipe(gunzip()).pipe(untar())))\n\t\t\t\t\t.pipe(filter('**/node'))\n\t\t\t\t\t.pipe(util.setExecutableBit('**'))\n\t\t\t\t\t.pipe(rename('node'))\n\t\t\t\t: extractAlpinefromDocker(nodeVersion, platform, arch);\n\t}\n}\n\nfunction packageTask(type, platform, arch, sourceFolderName, destinationFolderName) {\n\tconst destination = path.join(BUILD_ROOT, destinationFolderName);\n\n\treturn () => {\n\t\tconst json = require('gulp-json-editor');\n\n\t\tconst src = gulp.src(sourceFolderName + '/**', { base: '.' })\n\t\t\t.pipe(rename(function (path) { path.dirname = path.dirname.replace(new RegExp('^' + sourceFolderName), 'out'); }))\n\t\t\t.pipe(util.setExecutableBit(['**/*.sh']))\n\t\t\t.pipe(filter(['**', '!**/*.js.map']));\n\n\t\tconst workspaceExtensionPoints = ['debuggers', 'jsonValidation'];\n\t\tconst isUIExtension = (manifest) => {\n\t\t\tswitch (manifest.extensionKind) {\n\t\t\t\tcase 'ui': return true;\n\t\t\t\tcase 'workspace': return false;\n\t\t\t\tdefault: {\n\t\t\t\t\tif (manifest.main) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif (manifest.contributes && Object.keys(manifest.contributes).some(key => workspaceExtensionPoints.indexOf(key) !== -1)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\t// Default is UI Extension\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\tconst localWorkspaceExtensions = glob.sync('extensions/*/package.json')\n\t\t\t.filter((extensionPath) => {\n\t\t\t\tif (type === 'reh-web') {\n\t\t\t\t\treturn true; // web: ship all extensions for now\n\t\t\t\t}\n\n\t\t\t\t// Skip shipping UI extensions because the client side will have them anyways\n\t\t\t\t// and they'd just increase the download without being used\n\t\t\t\tconst manifest = JSON.parse(fs.readFileSync(path.join(REPO_ROOT, extensionPath)).toString());\n\t\t\t\treturn !isUIExtension(manifest);\n\t\t\t}).map((extensionPath) => path.basename(path.dirname(extensionPath)))\n\t\t\t.filter(name => name !== 'vscode-api-tests' && name !== 'vscode-test-resolver'); // Do not ship the test extensions\n\t\tconst marketplaceExtensions = JSON.parse(fs.readFileSync(path.join(REPO_ROOT, 'product.json'), 'utf8')).builtInExtensions\n\t\t\t.filter(entry => !entry.platforms || new Set(entry.platforms).has(platform))\n\t\t\t.filter(entry => !entry.clientOnly)\n\t\t\t.map(entry => entry.name);\n\t\tconst extensionPaths = [...localWorkspaceExtensions, ...marketplaceExtensions]\n\t\t\t.map(name => `.build/extensions/${name}/**`);\n\n\t\tconst extensions = gulp.src(extensionPaths, { base: '.build', dot: true });\n\t\tconst extensionsCommonDependencies = gulp.src('.build/extensions/node_modules/**', { base: '.build', dot: true });\n\t\tconst sources = es.merge(src, extensions, extensionsCommonDependencies)\n\t\t\t.pipe(filter(['**', '!**/*.js.map'], { dot: true }));\n\n\t\tlet version = packageJson.version;\n\t\tconst quality = product.quality;\n\n\t\tif (quality && quality !== 'stable') {\n\t\t\tversion += '-' + quality;\n\t\t}\n\n\t\tconst name = product.nameShort;\n\t\tconst packageJsonStream = gulp.src(['remote/package.json'], { base: 'remote' })\n\t\t\t.pipe(json({ name, version, dependencies: undefined, optionalDependencies: undefined }));\n\n\t\tconst date = new Date().toISOString();\n\n\t\tconst productJsonStream = gulp.src(['product.json'], { base: '.' })\n\t\t\t.pipe(json({ commit, date, version }));\n\n\t\tconst license = gulp.src(['remote/LICENSE'], { base: 'remote', allowEmpty: true });\n\n\t\tconst jsFilter = util.filter(data => !data.isDirectory() && /\\.js$/.test(data.path));\n\n\t\tconst productionDependencies = getProductionDependencies(REMOTE_FOLDER);\n\t\tconst dependenciesSrc = productionDependencies.map(d => path.relative(REPO_ROOT, d.path)).map(d => [`${d}/**`, `!${d}/**/{test,tests}/**`, `!${d}/.bin/**`]).flat();\n\t\tconst deps = gulp.src(dependenciesSrc, { base: 'remote', dot: true })\n\t\t\t// filter out unnecessary files, no source maps in server build\n\t\t\t.pipe(filter(['**', '!**/package-lock.json', '!**/yarn.lock', '!**/*.js.map']))\n\t\t\t.pipe(util.cleanNodeModules(path.join(__dirname, '.moduleignore')))\n\t\t\t.pipe(util.cleanNodeModules(path.join(__dirname, `.moduleignore.${process.platform}`)))\n\t\t\t.pipe(jsFilter)\n\t\t\t.pipe(util.stripSourceMappingURL())\n\t\t\t.pipe(jsFilter.restore);\n\n\t\tconst nodePath = `.build/node/v${nodeVersion}/${platform}-${arch}`;\n\t\tconst node = gulp.src(`${nodePath}/**`, { base: nodePath, dot: true });\n\n\t\tlet web = [];\n\t\tif (type === 'reh-web') {\n\t\t\tweb = [\n\t\t\t\t'resources/server/favicon.ico',\n\t\t\t\t'resources/server/code-192.png',\n\t\t\t\t'resources/server/code-512.png',\n\t\t\t\t'resources/server/manifest.json'\n\t\t\t].map(resource => gulp.src(resource, { base: '.' }).pipe(rename(resource)));\n\t\t}\n\n\t\tconst all = es.merge(\n\t\t\tpackageJsonStream,\n\t\t\tproductJsonStream,\n\t\t\tlicense,\n\t\t\tsources,\n\t\t\tdeps,\n\t\t\tnode,\n\t\t\t...web\n\t\t);\n\n\t\tlet result = all\n\t\t\t.pipe(util.skipDirectories())\n\t\t\t.pipe(util.fixWin32DirectoryPermissions());\n\n\t\tif (platform === 'win32') {\n\t\t\tresult = es.merge(result,\n\t\t\t\tgulp.src('resources/server/bin/remote-cli/code.cmd', { base: '.' })\n\t\t\t\t\t.pipe(replace('@@VERSION@@', version))\n\t\t\t\t\t.pipe(replace('@@COMMIT@@', commit))\n\t\t\t\t\t.pipe(replace('@@APPNAME@@', product.applicationName))\n\t\t\t\t\t.pipe(rename(`bin/remote-cli/${product.applicationName}.cmd`)),\n\t\t\t\tgulp.src('resources/server/bin/helpers/browser.cmd', { base: '.' })\n\t\t\t\t\t.pipe(replace('@@VERSION@@', version))\n\t\t\t\t\t.pipe(replace('@@COMMIT@@', commit))\n\t\t\t\t\t.pipe(replace('@@APPNAME@@', product.applicationName))\n\t\t\t\t\t.pipe(rename(`bin/helpers/browser.cmd`)),\n\t\t\t\tgulp.src('resources/server/bin/code-server.cmd', { base: '.' })\n\t\t\t\t\t.pipe(rename(`bin/${product.serverApplicationName}.cmd`)),\n\t\t\t);\n\t\t} else if (platform === 'linux' || platform === 'alpine' || platform === 'darwin') {\n\t\t\tresult = es.merge(result,\n\t\t\t\tgulp.src(`resources/server/bin/remote-cli/${platform === 'darwin' ? 'code-darwin.sh' : 'code-linux.sh'}`, { base: '.' })\n\t\t\t\t\t.pipe(replace('@@VERSION@@', version))\n\t\t\t\t\t.pipe(replace('@@COMMIT@@', commit))\n\t\t\t\t\t.pipe(replace('@@APPNAME@@', product.applicationName))\n\t\t\t\t\t.pipe(rename(`bin/remote-cli/${product.applicationName}`))\n\t\t\t\t\t.pipe(util.setExecutableBit()),\n\t\t\t\tgulp.src(`resources/server/bin/helpers/${platform === 'darwin' ? 'browser-darwin.sh' : 'browser-linux.sh'}`, { base: '.' })\n\t\t\t\t\t.pipe(replace('@@VERSION@@', version))\n\t\t\t\t\t.pipe(replace('@@COMMIT@@', commit))\n\t\t\t\t\t.pipe(replace('@@APPNAME@@', product.applicationName))\n\t\t\t\t\t.pipe(rename(`bin/helpers/browser.sh`))\n\t\t\t\t\t.pipe(util.setExecutableBit()),\n\t\t\t\tgulp.src(`resources/server/bin/${platform === 'darwin' ? 'code-server-darwin.sh' : 'code-server-linux.sh'}`, { base: '.' })\n\t\t\t\t\t.pipe(rename(`bin/${product.serverApplicationName}`))\n\t\t\t\t\t.pipe(util.setExecutableBit())\n\t\t\t);\n\t\t}\n\n\t\treturn result.pipe(vfs.dest(destination));\n\t};\n}\n\n/**\n * @param {object} product The parsed product.json file contents\n */\nfunction tweakProductForServerWeb(product) {\n\tconst result = { ...product };\n\tdelete result.webEndpointUrlTemplate;\n\treturn result;\n}\n\n['reh', 'reh-web'].forEach(type => {\n\tconst optimizeTask = task.define(`optimize-vscode-${type}`, task.series(\n\t\tutil.rimraf(`out-vscode-${type}`),\n\t\toptimize.optimizeTask(\n\t\t\t{\n\t\t\t\tout: `out-vscode-${type}`,\n\t\t\t\tamd: {\n\t\t\t\t\tsrc: 'out-build',\n\t\t\t\t\tentryPoints: (type === 'reh' ? serverEntryPoints : serverWithWebEntryPoints).flat(),\n\t\t\t\t\totherSources: [],\n\t\t\t\t\tresources: type === 'reh' ? serverResources : serverWithWebResources,\n\t\t\t\t\tloaderConfig: optimize.loaderConfig(),\n\t\t\t\t\tinlineAmdImages: true,\n\t\t\t\t\tbundleInfo: undefined,\n\t\t\t\t\tfileContentMapper: createVSCodeWebFileContentMapper('.build/extensions', type === 'reh-web' ? tweakProductForServerWeb(product) : product)\n\t\t\t\t},\n\t\t\t\tcommonJS: {\n\t\t\t\t\tsrc: 'out-build',\n\t\t\t\t\tentryPoints: [\n\t\t\t\t\t\t'out-build/server-main.js',\n\t\t\t\t\t\t'out-build/server-cli.js'\n\t\t\t\t\t],\n\t\t\t\t\tplatform: 'node',\n\t\t\t\t\texternal: [\n\t\t\t\t\t\t'minimist',\n\t\t\t\t\t\t// TODO: we cannot inline `product.json` because\n\t\t\t\t\t\t// it is being changed during build time at a later\n\t\t\t\t\t\t// point in time (such as `checksums`)\n\t\t\t\t\t\t'../product.json',\n\t\t\t\t\t\t'../package.json'\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t}\n\t\t)\n\t));\n\n\tconst minifyTask = task.define(`minify-vscode-${type}`, task.series(\n\t\toptimizeTask,\n\t\tutil.rimraf(`out-vscode-${type}-min`),\n\t\toptimize.minifyTask(`out-vscode-${type}`, `https://ticino.blob.core.windows.net/sourcemaps/${commit}/core`)\n\t));\n\tgulp.task(minifyTask);\n\n\tBUILD_TARGETS.forEach(buildTarget => {\n\t\tconst dashed = (str) => (str ? `-${str}` : ``);\n\t\tconst platform = buildTarget.platform;\n\t\tconst arch = buildTarget.arch;\n\n\t\t['', 'min'].forEach(minified => {\n\t\t\tconst sourceFolderName = `out-vscode-${type}${dashed(minified)}`;\n\t\t\tconst destinationFolderName = `vscode-${type}${dashed(platform)}${dashed(arch)}`;\n\n\t\t\tconst serverTaskCI = task.define(`vscode-${type}${dashed(platform)}${dashed(arch)}${dashed(minified)}-ci`, task.series(\n\t\t\t\tgulp.task(`node-${platform}-${arch}`),\n\t\t\t\tutil.rimraf(path.join(BUILD_ROOT, destinationFolderName)),\n\t\t\t\tpackageTask(type, platform, arch, sourceFolderName, destinationFolderName)\n\t\t\t));\n\t\t\tgulp.task(serverTaskCI);\n\n\t\t\tconst serverTask = task.define(`vscode-${type}${dashed(platform)}${dashed(arch)}${dashed(minified)}`, task.series(\n\t\t\t\tcompileBuildTask,\n\t\t\t\tcompileExtensionsBuildTask,\n\t\t\t\tcompileExtensionMediaBuildTask,\n\t\t\t\tminified ? minifyTask : optimizeTask,\n\t\t\t\tserverTaskCI\n\t\t\t));\n\t\t\tgulp.task(serverTask);\n\t\t});\n\t});\n});\n", "fileName": "./2.js"}, "diffs": [{"originalRange": "[141,141)", "modifiedRange": "[141,142)", "innerChanges": null}, {"originalRange": "[144,148)", "modifiedRange": "[145,145)", "innerChanges": null}, {"originalRange": "[160,160)", "modifiedRange": "[157,164)", "innerChanges": null}, {"originalRange": "[222,234)", "modifiedRange": "[226,234)", "innerChanges": [{"originalRange": "[222,17 -> 222,19 EOL]", "modifiedRange": "[226,17 -> 226,17 EOL]"}, {"originalRange": "[223,4 -> 223,28]", "modifiedRange": "[227,4 -> 227,37]"}, {"originalRange": "[223,32 -> 223,49]", "modifiedRange": "[227,41 -> 227,48]"}, {"originalRange": "[223,54 -> 223,65 EOL]", "modifiedRange": "[227,53 -> 227,62 EOL]"}, {"originalRange": "[224,4 -> 224,21]", "modifiedRange": "[228,4 -> 228,25]"}, {"originalRange": "[224,25 -> 224,29]", "modifiedRange": "[228,29 -> 228,55]"}, {"originalRange": "[224,43 -> 225,63]", "modifiedRange": "[228,69 -> 228,108]"}, {"originalRange": "[225,78 -> 226,8]", "modifiedRange": "[228,123 -> 228,152]"}, {"originalRange": "[226,22 -> 226,25 EOL]", "modifiedRange": "[228,166 -> 228,169 EOL]"}, {"originalRange": "[227,5 -> 227,30]", "modifiedRange": "[229,5 -> 229,25]"}, {"originalRange": "[227,33 -> 227,42]", "modifiedRange": "[229,28 -> 229,32]"}, {"originalRange": "[227,45 -> 227,93 EOL]", "modifiedRange": "[229,35 -> 229,67 EOL]"}, {"originalRange": "[228,5 -> 228,51 EOL]", "modifiedRange": "[230,5 -> 230,30 EOL]"}, {"originalRange": "[229,6 -> 230,6 EOL]", "modifiedRange": "[231,6 -> 231,40 EOL]"}, {"originalRange": "[231,4 -> 232,42]", "modifiedRange": "[232,4 -> 232,19]"}, {"originalRange": "[232,48 -> 232,69]", "modifiedRange": "[232,25 -> 233,32]"}, {"originalRange": "[232,72 -> 233,4 EOL]", "modifiedRange": "[233,35 -> 233,60 EOL]"}]}]}