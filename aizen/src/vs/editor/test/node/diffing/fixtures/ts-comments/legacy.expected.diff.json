{"original": {"content": "interface Test {\n    /**\n     * <PERSON><PERSON> +/- indicators for added/deleted changes.\n     * Defaults to true.\n     */\n    renderIndicators?: boolean;\n    /**\n     * Original model should be editable?\n     * Defaults to false.\n     */\n    originalEditable?: boolean;\n}", "fileName": "./1.tst"}, "modified": {"content": "interface Test {\n    /**\n     * <PERSON>der +/- indicators for added/deleted changes.\n     * Defaults to true.\n     */\n    renderIndicators?: boolean;\n    /**\n     * Shows icons in the glyph margin to revert changes.\n     * Default to true.\n     */\n    renderMarginRevertIcon?: boolean;\n    /**\n     * Original model should be editable?\n     * Defaults to false.\n     */\n    originalEditable?: boolean;\n}", "fileName": "./2.tst"}, "diffs": [{"originalRange": "[8,8)", "modifiedRange": "[8,13)", "innerChanges": null}]}