/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .merge-editor .code-view > .header {
	padding: 0 10px;
	height: 30px;
	display: flex;
	align-content: center;
	overflow: hidden;
}

.monaco-workbench .merge-editor .code-view > .header > span {
	align-self: center;
	text-overflow: ellipsis;
	overflow: hidden;
	padding-right: 6px;
	white-space: nowrap;
}

.monaco-workbench .merge-editor .code-view > .header > span.title {
	flex-shrink: 0;
}

.monaco-workbench .merge-editor .code-view > .header > span.description {
	flex-shrink: 0;
	display: flex;
	font-size: 12px;
	align-items: center;
	color: var(--vscode-descriptionForeground);
}

.monaco-workbench .merge-editor .code-view.result > .header > .description {
	display: inline;
	flex-shrink: 1;
}
.monaco-workbench .merge-editor .code-view.result > .header > .detail {
	flex-shrink: 0;
}
.monaco-workbench .merge-editor .code-view.result > .header > .toolbar {
	flex-shrink: 0;
}

.monaco-workbench .merge-editor .code-view > .header > span.description .codicon {
	font-size: 14px;
	color: var(--vscode-descriptionForeground);
}

.monaco-workbench .merge-editor .code-view > .header > span.detail {
	margin-left: auto;
	font-size: 12px;
	color: var(--vscode-descriptionForeground);
}

.monaco-workbench .merge-editor .code-view > .header > span.detail .codicon {
	font-size: 13px;
}

/* for input1|2 -> align details to the left  */
.monaco-workbench .merge-editor .code-view.input > .header > span.detail::before {
	content: '•';
	opacity: .5;
	padding-right: 3px;
}
.monaco-workbench .merge-editor .code-view.input > .header > span.detail {
	margin-left: 0;
}
.monaco-workbench .merge-editor .code-view.input > .header > span.toolbar {
	flex-shrink: 0;
	margin-left: auto;
}


.monaco-workbench .merge-editor .code-view > .container {
	display: flex;
	flex-direction: row;
}

.monaco-workbench .merge-editor .code-view > .container > .gutter {
	width: 24px;
	position: relative;
	overflow: hidden;
	flex-shrink: 0;
	flex-grow: 0;
}

.monaco-workbench .merge-editor .merge-editor-diff {
	background-color: var(--vscode-mergeEditor-change-background);
}

.monaco-workbench .merge-editor .merge-editor-diff-word {
	background-color: var(--vscode-mergeEditor-change-word-background);
}

/* BEGIN: .merge-editor-block */

.monaco-workbench .merge-editor .merge-editor-block:not(.handled):not(.focused) {
	border: 1px solid var(--vscode-mergeEditor-conflict-unhandledUnfocused-border);
}

.monaco-workbench .merge-editor .merge-editor-block:not(.handled).focused {
	border: 2px solid var(--vscode-mergeEditor-conflict-unhandledFocused-border);
}

.monaco-workbench .merge-editor .merge-editor-block.handled:not(.focused) {
	border: 1px solid var(--vscode-mergeEditor-conflict-handledUnfocused-border);
}

.monaco-workbench .merge-editor .merge-editor-block.handled.focused {
	border: 1px solid var(--vscode-mergeEditor-conflict-handledFocused-border);
}

.monaco-workbench .merge-editor .merge-editor-simplified.input.i1, .merge-editor-block.use-simplified-decorations.input.i1 {
	background-color: var(--vscode-mergeEditor-conflict-input1-background);
}

.monaco-workbench .merge-editor .merge-editor-simplified.input.i2, .merge-editor-block.use-simplified-decorations.input.i2 {
	background-color: var(--vscode-mergeEditor-conflict-input2-background);
}

/* END: .merge-editor-block */

.gutter.monaco-editor > div {
	position: absolute;
}

.merge-accept-gutter-marker {
	width: 28px;
	margin-left: 4px;
}

.merge-accept-gutter-marker .background {
	height: 100%;
	width: 50%;
	position: absolute;
}

.merge-accept-gutter-marker.multi-line.focused .background {
	border: 2px solid var(--vscode-mergeEditor-conflict-unhandledFocused-border);
	border-right: 0;
}

.merge-accept-gutter-marker.multi-line .background {
	border: 2px solid var(--vscode-mergeEditor-conflict-unhandledUnfocused-border);
	border-right: 0;
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
}

.merge-accept-gutter-marker.multi-line.handled.focused .background {
	border: 2px solid var(--vscode-mergeEditor-conflict-handledFocused-border);
	border-right: 0;
}

.merge-accept-gutter-marker.multi-line.handled .background {
	border: 2px solid var(--vscode-checkbox-border);
	border-right: 0;
}


.focused .accept-conflict-group.monaco-custom-toggle {
	border: 1px solid var(--vscode-mergeEditor-conflict-unhandledFocused-border);
}

.accept-conflict-group.monaco-custom-toggle {
	border: 1px solid var(--vscode-mergeEditor-conflict-unhandledUnfocused-border);
}

.handled.focused .accept-conflict-group.monaco-custom-toggle {
	border: 1px solid var(--vscode-mergeEditor-conflict-handledFocused-border);
}

.handled .accept-conflict-group.monaco-custom-toggle {
	border: 1px solid var(--vscode-checkbox-border);
}

.merge-accept-gutter-marker.multi-line .background {
	left: 8px;
	width: 10px;
}

.merge-accept-gutter-marker .checkbox {
	width: 100%;
	position: absolute;
}

.accept-conflict-group.monaco-custom-toggle {
	height: 18px;
	width: 18px;
	border-radius: 3px;
	margin-right: 0px;
	margin-left: 0px;
	padding: 0px;
	opacity: 1;
	background-size: 16px !important;
	background-color: var(--vscode-checkbox-border);
}

.merge-accept-gutter-marker .checkbox-background {
	display: flex;
	background: var(--vscode-editor-background);
}

.conflict-zone-root {
	background-color: var(--vscode-mergeEditor-change-background);
	border: 1px solid var(--vscode-mergeEditor-conflict-unhandledUnfocused-border);
	height: 90%;
	display: flex;
	align-items: center;
	align-content: center;
}

.conflict-zone-root .dots {
	margin: 0 10px;
}

.conflict-zone-root pre {
	display: 'inline';
	font-family: var(--monaco-monospace-font);
}

.conflict-zone-root .text {
	background: var(--vscode-mergeEditor-conflictingLines-background);
	margin-left: auto;
	padding: 0 8px;
	display: flex;
	align-items: center;
	height: 100%;
	white-space: nowrap;
	overflow: hidden;
}


.focused.conflict-zone .conflict-zone-root {
	border: 1px solid var(--vscode-mergeEditor-conflict-unhandledFocused-border);
}

.merge-editor-conflict-actions {
	margin: 0px 3px;
	overflow: hidden;
	display: inline-block;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: var(--vscode-editorCodeLens-foreground)
}

.merge-editor-conflict-actions > span,
.merge-editor-conflict-actions > a {
	user-select: none;
	-webkit-user-select: none;
	white-space: nowrap;
}

.merge-editor-conflict-actions > a {
	text-decoration: none;
}

.merge-editor-conflict-actions > a:hover {
	cursor: pointer;
	color: var(--vscode-editorLink-activeForeground) !important;
}

.merge-editor-conflict-actions > a:hover .codicon {
	color: var(--vscode-editorLink-activeForeground) !important;
}

.merge-editor-conflict-actions .codicon {
	vertical-align: middle;
	color: currentColor !important;
	color: var(--vscode-editorCodeLens-foreground);
}

.merge-editor-conflict-actions > a:hover .codicon::before {
	cursor: pointer;
}

.fixed-zone-widget {
	width: 100%;
}

.merge-editor-diff-empty-word.base {
	margin-left: 3px;
	border-left: solid var(--vscode-mergeEditor-changeBase-word-background) 3px;
}

.merge-editor-diff-empty-word.input {
	margin-left: 3px;
	border-left: solid var(--vscode-mergeEditor-change-word-background) 3px;
}

.merge-editor-diff-word.base {
	background-color: var(--vscode-mergeEditor-changeBase-word-background);
}

.merge-editor-diff.base {
	background-color: var(--vscode-mergeEditor-changeBase-background);
}
