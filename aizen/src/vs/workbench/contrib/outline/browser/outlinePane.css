/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .outline-pane {
	display: flex;
	flex-direction: column;
}

.monaco-workbench .outline-pane .outline-progress {
	width: 100%;
	height: 2px;
	padding-bottom: 3px;
	position: absolute;
}

.monaco-workbench .outline-pane .outline-progress .monaco-progress-container {
	height: 2px;
}

.monaco-workbench .outline-pane .outline-progress .monaco-progress-container .progress-bit {
	height: 2px;
}

.monaco-workbench .outline-pane .outline-tree {
	height: 100%;
}

.monaco-workbench .outline-pane .outline-message {
	display: none;
	padding: 10px 22px 0 22px;
	opacity: 0.5;
	position: absolute;
	pointer-events: none;
	z-index: 1;
}

.monaco-workbench .outline-pane.message .outline-message {
	display: inherit;
}

.monaco-workbench .outline-pane.message .outline-progress {
	display: none;
}
