/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.defineKeybindingWidget {
	padding: 10px;
	position: absolute;
}

.defineKeybindingWidget .message {
	width: 400px;
	text-align: center;
}

.defineKeybindingWidget .monaco-inputbox,
.defineKeybindingWidget .output,
.defineKeybindingWidget .existing {
	margin-top:10px;
	width: 400px;
	display: block;
	text-align: center;
}

.defineKeybindingWidget .input {
	text-align: center;
}

.defineKeybindingWidget .output {
	display: flex;
	justify-content: center;
}

.defineKeybindingWidget .existing .existingText {
	text-decoration: underline;
	cursor: pointer;
}

.defineKeybindingWidget .output .monaco-keybinding {
	margin: 0px 4px;
}

/* Editor decorations */
.monaco-editor .keybindingInfo {
	box-shadow:	inset 0 0 0 1px #B9B9B9;
	background-color: rgba(100, 100, 250, 0.2);
}

.monaco-editor .keybindingError {
	box-shadow:	inset 0 0 0 1px #B9B9B9;
	background-color: rgba(250, 100, 100, 0.2);
}
