/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.preferences-editor {
	display: flex;
	flex-direction: column;
}

.preferences-editor > .preferences-header {
	padding-left: 27px;
	padding-right: 32px;
	padding-bottom: 11px;
	padding-top: 11px;
}

.preferences-editor .deprecation-warning {
	display: flex;
	margin-top: 4px;
}

.preferences-editor .deprecation-warning .icon {
	margin-right: 3px;
}

.preferences-editor .deprecation-warning .learnMore-button {
	margin-left: 3px;
	text-decoration: underline;
}

.preferences-editor > .preferences-editors-container.side-by-side-preferences-editor {
	flex: 1;
}

.preferences-editor > .preferences-editors-container.side-by-side-preferences-editor .preferences-header-container {
	line-height: 28px;
}

.settings-tabs-widget > .monaco-action-bar .action-item.disabled {
	display: none;
}

.settings-tabs-widget > .monaco-action-bar .action-item {
	max-width: 300px;
	overflow: hidden;
	text-overflow: ellipsis;
}

.default-preferences-editor-container > .preferences-header-container > .default-preferences-header,
.settings-tabs-widget > .monaco-action-bar .action-item .action-label {
	text-transform: uppercase;
	font-size: 11px;
	margin-right: 5px;
	cursor: pointer;
	display: flex;
	overflow: hidden;
	text-overflow: ellipsis;
}

.default-preferences-editor-container > .preferences-header-container > .default-preferences-header,
.preferences-editor .settings-tabs-widget > .monaco-action-bar .action-item .action-label {
	margin-left: 33px;
}

.settings-tabs-widget > .monaco-action-bar .action-item .action-label {
	display: block;
	padding: 0px;
	border-radius: initial;
	background: none !important;
}

.settings-tabs-widget > .monaco-action-bar .action-item .action-label.folder-settings {
	display: flex;
}

.default-preferences-editor-container > .preferences-header-container > .default-preferences-header,
.settings-tabs-widget > .monaco-action-bar .action-item {
	padding: 3px 0px;
}

.settings-tabs-widget > .monaco-action-bar .action-item .action-title {
	text-overflow: ellipsis;
	overflow: hidden;
}

.settings-tabs-widget > .monaco-action-bar .action-item .action-details {
	text-transform: none;
	margin-left: 0.5em;
	font-size: 10px;
	opacity: 0.7;
}

.settings-tabs-widget .monaco-action-bar .action-item .dropdown-icon {
	padding-left: 0.3em;
	padding-top: 8px;
	font-size: 12px;
}

.settings-tabs-widget .monaco-action-bar .action-item .dropdown-icon.hide {
	display: none;
}

.settings-tabs-widget > .monaco-action-bar .action-item .action-label {
	color: var(--vscode-panelTitle-inactiveForeground);
}

.settings-tabs-widget > .monaco-action-bar .action-item .action-label.checked,
.settings-tabs-widget > .monaco-action-bar .action-item .action-label:hover {
	color: var(--vscode-panelTitle-activeForeground);
	border-bottom: 1px solid var(--vscode-panelTitle-activeBorder);
	outline: 1px solid var(--vscode-contrastActiveBorder, transparent);
	outline-offset: -1px;
}

.settings-tabs-widget > .monaco-action-bar .action-item .action-label:focus {
	border-bottom: 1px solid var(--vscode-focusBorder);
	outline: 1px solid transparent;
	outline-offset: -1px;
}

.settings-tabs-widget > .monaco-action-bar .action-item .action-label:not(.checked):hover {
	outline-style: dashed;
}

.preferences-header > .settings-header-widget {
	flex: 1;
	display: flex;
	position: relative;
	align-self: stretch;
}

.settings-header-widget > .settings-search-controls > .settings-count-widget {
	margin: 6px 0px;
	padding: 0px 8px;
	border-radius: 2px;
	float: left;
}

.settings-header-widget > .settings-search-controls {
	position: absolute;
	right: 10px;
}

.settings-header-widget > .settings-search-controls > .settings-count-widget.hide {
	display: none;
}

.settings-header-widget > .settings-search-container {
	flex: 1;
}

.settings-header-widget > .settings-search-container > .settings-search-input {
	vertical-align: middle;
}

.settings-header-widget > .settings-search-container > .settings-search-input > .monaco-inputbox {
	height: 30px;
}

.monaco-workbench.vs .settings-header-widget > .settings-search-container > .settings-search-input > .monaco-inputbox {
	border: 1px solid #ddd;
}

.settings-header-widget > .settings-search-container > .settings-search-input > .monaco-inputbox .input {
	font-size: 14px;
	padding-left:10px;
}

.monaco-editor .view-zones > .settings-header-widget {
	z-index: 1;
}

.monaco-editor .settings-header-widget .title-container {
	display: flex;
	user-select: none;
	-webkit-user-select: none;
}

.monaco-editor .settings-header-widget .title-container .title {
	font-weight: bold;
	white-space: nowrap;
	text-transform: uppercase;
}

.monaco-editor .settings-header-widget .title-container .message {
	white-space: nowrap;
}

.monaco-editor .settings-group-title-widget {
	z-index: 1;
}

.monaco-editor .settings-group-title-widget .title-container {
	width: 100%;
	cursor: pointer;
	font-weight: bold;
	user-select: none;
	-webkit-user-select: none;
	display: flex;
}


.monaco-editor .settings-group-title-widget .title-container .title {
	white-space: nowrap;
	overflow: hidden;
}

.monaco-editor.vs-dark .settings-group-title-widget .title-container.focused,
.monaco-editor.vs .settings-group-title-widget .title-container.focused {
	outline: none !important;
}

.monaco-editor .settings-group-title-widget .title-container.focused,
.monaco-editor .settings-group-title-widget .title-container:hover {
	background-color: rgba(153, 153, 153, 0.2);
}

.monaco-editor.hc-black .settings-group-title-widget .title-container.focused {
	outline: 1px dotted #f38518;
}

.monaco-editor.hc-light .settings-group-title-widget .title-container.focused {
	outline: 1px dotted #0F4A85;
}

.monaco-editor .settings-group-title-widget .title-container .codicon {
	margin: 0 2px;
	width: 16px;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.monaco-editor .dim-configuration {
	color: #b1b1b1;
}

.codicon-settings-edit:hover {
	cursor: pointer;
}
