/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.review-widget {
	width: 100%;
	position: absolute;
}

.monaco-editor .review-widget,
.monaco-editor .review-widget {
	background-color: var(--vscode-peekViewResult-background);
}
.review-widget .hidden {
	display: none !important;
}

.review-widget .body {
	overflow: hidden;
}

.review-widget .body .review-comment {
	padding: 8px 16px 8px 20px;
	display: flex;
}

@keyframes monaco-review-widget-focus {
	0% {
		background: var(--vscode-peekViewResult-selectionBackground);
	}

	100% {
		background: transparent;
	}
}

.review-widget .body .review-comment.focus {
	animation: monaco-review-widget-focus 3s ease 0s;
}
.review-widget .body .review-comment .comment-actions {
	margin-left: auto;
}

.review-widget .body .review-comment .comment-actions .monaco-toolbar {
	height: 22px;
}

.review-widget .body .review-comment .comment-title .comment-header-info {
	overflow: hidden;
	text-overflow: ellipsis;
}

.review-widget .body .review-comment .comment-title {
	display: flex;
	width: 100%;
}

.review-widget .body .review-comment .comment-title .action-label.codicon {
	line-height: 18px;
}

.review-widget .body .review-comment .comment-title .monaco-dropdown .toolbar-toggle-more {
	width: 16px;
	height: 18px;
	line-height: 18px;
	vertical-align: middle;
}

.review-widget .body .comment-body blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

.review-widget .body .review-comment .avatar-container {
	margin-top: 4px !important;
}

.review-widget .body .review-comment .avatar-container img.avatar {
	height: 28px;
	width: 28px;
	display: inline-block;
	overflow: hidden;
	line-height: 1;
	vertical-align: middle;
	border-radius: 3px;
	border-style: none;
}

.review-widget .body .comment-reactions .monaco-text-button {
	margin: 0 7px 0 0;
	width: 30px;
	background-color: transparent;
	border: 1px solid grey;
	border-radius: 3px;
}

.review-widget .body .review-comment .review-comment-contents {
	padding-left: 20px;
	user-select: text;
	-webkit-user-select: text;
	width: 100%;
	overflow: hidden;
}

.review-widget .body pre {
	overflow: auto;
	word-wrap: normal;
	white-space: pre;
}


.review-widget .body .review-comment .review-comment-contents .author {
	line-height: 22px;
}


.review-widget .body .review-comment .review-comment-contents .isPending {
	line-height: 22px;
	margin: 0 5px 0 5px;
	padding: 0 2px 0 2px;
	font-style: italic;
}

.review-widget .body .review-comment .review-comment-contents .timestamp {
	line-height: 22px;
	margin: 0 5px 0 5px;
	padding: 0 2px 0 2px;
}

.review-widget .body .review-comment .review-comment-contents .comment-body .comment-body-plainstring {
	white-space: pre-wrap;
}

.review-widget .body .review-comment .review-comment-contents .comment-body {
	padding-top: 4px;
}

.review-widget .body .review-comment .review-comment-contents .comment-body-max-height {
	max-height: 20em;
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions {
	margin-top: 8px;
	min-height: 25px;
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions .action-item .action-label {
	padding: 1px 4px;
	white-space: pre;
	text-align: center;
	font-size: 12px;
	display: flex;
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions .action-item .action-label .reaction-icon {
	background-size: 14px;
	background-position: left center;
	background-repeat: no-repeat;
	width: 14px;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	display: inline-block;
	margin-right: 4px;
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions .action-item .action-label .reaction-label {
	line-height: 20px;
	margin-right: 4px;
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions .action-item a.action-label.toolbar-toggle-pickReactions {
	display: none;
	background-size: 16px;
	font-size: 16px;
	width: 26px;
	height: 16px;
	background-repeat: no-repeat;
	background-position: center;
	margin-top: 3px;
	border: none;
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions:hover .action-item a.action-label.toolbar-toggle-pickReactions {
	display: inline-block;
	background-size: 16px;
}

.review-widget .body .review-comment .comment-title .action-label {
	display: block;
	height: 16px;
	line-height: 16px;
	background-size: 16px;
	background-position: center center;
	background-repeat: no-repeat;
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions .action-item a.action-label {
	border: 1px solid;
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions .action-item a.action-label.disabled {
	opacity: 0.6;
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions .action-item a.action-label.active:hover {
	background-color: var(--vscode-statusBarItem-hoverBackground);
}

.review-widget .body .review-comment .review-comment-contents .comment-reactions .action-item a.action-label:active {
	background-color: var(--vscode-statusBarItem-activeBackground);
	border: 1px solid transparent;
}
.review-widget .body .review-comment .review-comment-contents a {
	cursor: pointer;
}

.review-widget .body .comment-body p,
.review-widget .body .comment-body ul {
	margin: 8px 0;
}

.review-widget .body .comment-body p:first-child,
.review-widget .body .comment-body ul:first-child {
	margin-top: 0;
}

.review-widget .body .comment-body p:last-child,
.review-widget .body.comment-body ul:last-child {
	margin-bottom: 0;
}

.review-widget .body .comment-body ul {
	padding-left: 20px;
}

.review-widget .body .comment-body li > p {
	margin-bottom: 0;
}

.review-widget .body .comment-body li > ul {
	margin-top: 0;
}

.review-widget .body .comment-body code {
	border-radius: 3px;
	padding: 0 0.4em;
}

.review-widget .body .comment-body span {
	white-space: pre;
}

.review-widget .body .comment-body img {
	max-width: 100%;
}

.review-widget .body .comment-form {
	margin: 8px 20px;
}

.review-widget .validation-error {
	display: inline-block;
	overflow: hidden;
	text-align: left;
	width: 100%;
	box-sizing: border-box;
	padding: 0.4em;
	font-size: 12px;
	line-height: 17px;
	min-height: 34px;
	margin-top: -1px;
	margin-left: -1px;
	word-wrap: break-word;
}


.review-widget .body .comment-additional-actions {
	margin: 10px 20px;
}

.review-widget .body .comment-additional-actions .section-separator {
	border-top: 1px solid var(--vscode-menu-separatorBackground);
	margin: 10px 0 14px;
}

.review-widget .body .comment-additional-actions .button-bar {
	display: flex;
	white-space: nowrap;
}

.review-widget .body .comment-additional-actions .monaco-button,
.review-widget .body .comment-additional-actions .monaco-text-button,
.review-widget .body .comment-additional-actions .monaco-button-dropdown {
	display: flex;
	width: auto;
}

.review-widget .body .comment-additional-actions .button-bar>.monaco-text-button,
.review-widget .body .comment-additional-actions .button-bar>.monaco-button-dropdown {
	margin: 0 10px 0 0;
}

.review-widget .body .comment-additional-actions .button-bar .monaco-text-button {
	padding: 4px 10px;
}


.review-widget .body .comment-additional-actions .codicon-drop-down-button {
	align-items: center;
}

.review-widget .body .monaco-editor {
	color: var(--vscode-editor-foreground);
}
.review-widget .body .comment-form.expand .review-thread-reply-button {
	display: none;
}

.review-widget .body .comment-form.expand .monaco-editor,
.review-widget .body .comment-form.expand .form-actions {
	display: block;
	box-sizing: content-box;
}

.review-widget .body .comment-form .review-thread-reply-button {
	text-align: left;
	display: block;
	width: 100%;
	resize: vertical;
	border-radius: 0;
	box-sizing: border-box;
	padding: 6px 12px;
	font-weight: 600;
	line-height: 20px;
	white-space: nowrap;
	border: 0px;
	outline: 1px solid transparent;
	background-color: var(--vscode-peekViewTitle-background);
	color: var(--vscode-editor-foreground);
	font-size: inherit;
	font-family: var(--monaco-monospace-font);
}

.review-widget .body .comment-form .review-thread-reply-button:focus {
	outline-style: solid;
	outline-width: 1px;
}

.review-widget .body .comment-form .monaco-editor,
.review-widget .body .comment-form .monaco-editor .monaco-editor-background,
.review-widget .body .edit-container .monaco-editor .monaco-editor-background {
	background-color: var(--vscode-peekViewTitle-background);
}

.review-widget .body .comment-form .monaco-editor,
.review-widget .body .edit-container .monaco-editor {
	width: 100%;
	min-height: 90px;
	max-height: 500px;
	border-radius: 3px;
	border: 0px;
	box-sizing: content-box;
	padding: 6px 0 6px 12px;
}

.review-widget .body .comment-form .monaco-editor,
.review-widget .body .comment-form .form-actions {
	display: none;
}

.review-widget .body .comment-form .form-actions,
.review-widget .body .edit-container .form-actions {
	overflow: auto;
	margin: 10px 0;
}

.review-widget .body .edit-container .form-actions {
	padding-top: 10px;
}

.review-widget .body .edit-textarea {
	margin: 5px 0 10px 0;
	margin-right: 12px;
}

.review-widget .body .comment-form .monaco-text-button,
.review-widget .body .edit-container .monaco-text-button {
	width: auto;
	padding: 4px 10px;
	margin-left: 5px;
}

.review-widget .body .form-actions .monaco-text-button {
	float: right;
}

.review-widget .head {
	box-sizing: border-box;
	display: flex;
	height: 100%;
}

.review-widget .head .review-title {
	display: inline-block;
	font-size: 13px;
	margin-left: 20px;
	cursor: default;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.review-widget .head .review-title .dirname:not(:empty) {
	font-size: 0.9em;
	margin-left: 0.5em;
}

.review-widget .head .review-actions {
	flex: 1;
	text-align: right;
	padding-right: 2px;
}

.review-widget .head .review-actions > .monaco-action-bar {
	display: inline-block;
}

.review-widget .head .review-actions > .monaco-action-bar,
.review-widget .head .review-actions > .monaco-action-bar > .actions-container {
	height: 100%;
}

.review-widget .action-item {
	min-width: 18px;
	min-height: 20px;
	margin-left: 4px;
}

.review-widget .head .review-actions > .monaco-action-bar .action-label {
	margin: 0;
	line-height: inherit;
	background-repeat: no-repeat;
	background-position: center center;
}

.review-widget .head .review-actions > .monaco-action-bar .action-label.codicon {
	margin: 0;
}

.review-widget > .body {
	border-top: 1px solid;
	position: relative;
}

.monaco-editor .comment-range-glyph {
	margin-left: 10px;
	width: 4px !important;
	cursor: pointer;
	z-index: 10;
}

div.preview.inline .monaco-editor .comment-range-glyph {
	display: none !important;
}

.monaco-editor .comment-diff-added {
	border-left-width: 3px;
	border-left-style: solid;
}

.monaco-editor .comment-diff-added,
.monaco-editor .comment-range-glyph.multiline-add {
	border-left-color: var(--vscode-editorGutter-commentRangeForeground);
}

.monaco-editor .comment-diff-added:before,
.monaco-editor .comment-range-glyph.line-hover:before {
	background: var(--vscode-editorGutter-commentRangeForeground);
}

.monaco-editor .comment-thread:before,
.monaco-editor .comment-thread-unresolved:before {
	background: var(--vscode-editorGutter-commentRangeForeground);
}

.monaco-editor .comment-thread-range {
	background-color: var(--vscode-editorCommentsWidget-rangeBackground);
}

.monaco-editor .comment-thread-range-current {
	background-color: var(--vscode-editorCommentsWidget-rangeActiveBackground);
}

.monaco-editor .margin-view-overlays .comment-range-glyph.line-hover,
.monaco-editor .margin-view-overlays .comment-range-glyph.comment-thread,
.monaco-editor .margin-view-overlays .comment-range-glyph.comment-thread-unresolved {
	margin-left: 13px;
}

.monaco-editor .margin-view-overlays > div:hover > .comment-range-glyph.comment-diff-added:before,
.monaco-editor .margin-view-overlays .comment-range-glyph.line-hover:before,
.monaco-editor .comment-range-glyph.comment-thread:before,
.monaco-editor .comment-range-glyph.comment-thread-unresolved:before {
	position: absolute;
	height: 100%;
	width: 9px;
	left: -6px;
	z-index: 10;
	color: var(--vscode-editorGutter-commentGlyphForeground);
	text-align: center;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
}

.monaco-editor .comment-range-glyph.comment-thread-unresolved:before {
	color: var(--vscode-editorGutter-commentUnresolvedGlyphForeground);
}

.monaco-editor .margin-view-overlays .comment-range-glyph.multiline-add {
	border-left-width: 3px;
	border-left-style: dotted;
	height: 16px;
	margin-top: 2px;
}

.monaco-editor .margin-view-overlays > div:hover > .comment-range-glyph.comment-diff-added:before,
.monaco-editor .margin-view-overlays .comment-range-glyph.line-hover:before {
	content: "\ea60";
	font-family: "codicon";
	border-radius: 3px;
	width: 18px !important;
	margin-left: -5px;
	padding-left: 1px;
}

.monaco-editor .comment-range-glyph.comment-thread,
.monaco-editor .comment-range-glyph.comment-thread-unresolved {
	z-index: 20;
}

.monaco-editor .comment-range-glyph.comment-thread:before,
.monaco-editor .comment-range-glyph.comment-thread-unresolved:before {
	font-family: "codicon";
	font-size: 13px;
	width: 18px !important;
	line-height: 100%;
	border-radius: 3px;
	z-index: 20;
	margin-left: -5px;
	padding-top: 1px;
	padding-left: 1px;
}

.monaco-editor .comment-range-glyph.comment-thread:before {
	content: "\ea6b";

}
.monaco-editor .comment-range-glyph.comment-thread-unresolved:before {
	content: "\ec0a";
}

.monaco-editor.inline-comment .margin-view-overlays .codicon-folding-expanded,
.monaco-editor.inline-comment .margin-view-overlays .codicon-folding-collapsed {
	margin-left: 11px;
}

.monaco-editor.inline-comment .margin-view-overlays .dirty-diff-glyph {
	margin-left: 25px;
}
