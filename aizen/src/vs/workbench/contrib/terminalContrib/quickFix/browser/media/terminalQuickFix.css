/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.xterm-screen .xterm-decoration-container .xterm-decoration.quick-fix {
	z-index: 7;
}

.monaco-workbench .terminal .terminal-command-decoration.quick-fix {
	color: var(--vscode-editorLightBulb-foreground) !important;
	background-color: var(--vscode-terminal-background, var(--vscode-panel-background));
}

.monaco-workbench .terminal .terminal-command-decoration.quick-fix.explainOnly {
	/* Use success background to blend in with the terminal better as it's lower priority. We will
	 * probably want to add an explicit color for this eventually. */
	color: var(--vscode-terminalCommandDecoration-successBackground) !important;
}
