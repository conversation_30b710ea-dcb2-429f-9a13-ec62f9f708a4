/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor .zone-widget .zone-widget-container.breakpoint-widget {
	display: flex;
	border-color: #007ACC;
}

.monaco-editor .zone-widget .zone-widget-container.breakpoint-widget .breakpoint-select-container {
	display: flex;
	justify-content: center;
	flex-direction: column;
	padding: 0 10px;
	flex-shrink: 0;
}

.monaco-editor .zone-widget .zone-widget-container.breakpoint-widget .breakpoint-select-container .monaco-select-box {
	min-width: 100px;
	min-height: 18px;
	padding: 2px 20px 2px 8px;
}

.monaco-editor .zone-widget .zone-widget-container.breakpoint-widget .breakpoint-select-container:after {
	right: 14px;
}

.monaco-editor .zone-widget .zone-widget-container.breakpoint-widget .inputContainer {
	flex: 1;
}
