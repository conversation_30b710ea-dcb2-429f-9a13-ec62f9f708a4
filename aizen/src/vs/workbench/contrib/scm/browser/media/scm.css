/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.scm-view {
	height: 100%;
	position: relative;
}

.scm-overflow-widgets-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 0;
	height: 0;
	overflow: visible;
	z-index: 5000;
}

.scm-view .monaco-tl-contents > div {
	padding-right: 12px;
	overflow: hidden;
}

.scm-view .count {
	display: flex;
	margin-left: 6px;
}

.scm-view .count.hidden {
	display: none;
}

.scm-view .scm-provider {
	display: flex;
	flex-direction: column;
	height: 100%;
	align-items: center;
	flex-flow: nowrap;
}

.scm-view.hide-provider-counts .scm-provider > .count,
.scm-view.auto-provider-counts .scm-provider > .count[data-count="0"] {
	display: none;
}

.scm-view .scm-provider > .label {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	min-width: 50px;
}

.scm-view .scm-provider > .label > .name {
	font-weight: bold;
}

.scm-view .scm-provider > .label > .description {
	opacity: 0.7;
	margin-left: 0.5em;
	font-size: 0.9em;
}

.scm-view .scm-provider > .actions {
	overflow: hidden;
	justify-content: flex-end;
}

.scm-view .monaco-editor .selected-text {
	border-radius: 0;
}

/**
 * The following rules are very specific because of inline drop down menus
 * https://github.com/microsoft/vscode/issues/101410
 */
.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item {
	padding-left: 4px;
	display: flex;
	align-items: center;
	min-width: 16px;
}

.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container  > .action-item > .action-label,
.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container  > .action-item > .monaco-dropdown > .dropdown-label > .action-label {
	display: flex;
	align-items: center;
	overflow: hidden;
}

.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container  > .action-item > .action-label > .codicon {
	font-size: 12px;
	justify-content: center;
}

.scm-view .scm-provider > .actions > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item:last-of-type {
	padding-right: 0;
}

.scm-view .scm-provider > .body {
	flex-grow: 1;
}

.scm-view .scm-provider > .status > .monaco-action-bar > .actions-container {
	border-color: var(--vscode-sideBar-border);
}

.scm-view .monaco-list-row {
	line-height: 22px;
}

.scm-view .monaco-list-row .history,
.scm-view .monaco-list-row .history-item-group,
.scm-view .monaco-list-row .resource-group {
	display: flex;
	height: 100%;
	align-items: center;
}

.scm-view .monaco-list-row .history-item-group .monaco-highlighted-label {
	display: flex;
	align-items: center;
}

.scm-view .monaco-list-row .history-item-group .monaco-icon-label,
.scm-view .monaco-list-row .history-item .monaco-icon-label {
	flex-grow: 1;
	align-items: center;
}

.scm-view .monaco-list-row .history-item-group .monaco-icon-label > .monaco-icon-label-container {
	display: flex;
}
.scm-view .monaco-list-row .history-item-group .monaco-icon-label > .monaco-icon-label-container .monaco-icon-description-container {
	overflow: hidden;
	text-overflow: ellipsis;
}

.scm-sync-view .monaco-list-row .monaco-icon-label .icon-container
.scm-sync-view .monaco-list-row .monaco-icon-label .icon-container {
	display: flex;
	font-size: 14px;
	padding-right: 4px;
}

.scm-sync-view .monaco-list-row .history-item .monaco-icon-label .icon-container {
	display: flex;
	font-size: 14px;
	padding-right: 4px;
}

.scm-sync-view .monaco-list-row .history-item .monaco-icon-label .avatar {
	width: 14px;
	height: 14px;
	border-radius: 14px;
}

.scm-view .monaco-list-row .history > .name,
.scm-view .monaco-list-row .history-item-group > .name,
.scm-view .monaco-list-row .resource-group > .name {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
}

.scm-view .monaco-list-row .resource {
	display: flex;
	height: 100%;
}

.scm-view .monaco-list-row .resource.faded {
	opacity: 0.7;
}

.scm-view .monaco-list-row .resource > .name {
	flex: 1;
	overflow: hidden;
}

.scm-view .monaco-list-row .resource > .name > .monaco-icon-label::after {
	margin-right: 3px;
}

.scm-view .monaco-list-row .resource > .decoration-icon {
	width: 16px;
	height: 100%;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	margin-right: 8px;
}

.scm-view .monaco-list-row .resource > .decoration-icon.codicon {
	margin-right: 0;
	margin-top: 3px;
}

.scm-view .monaco-list .monaco-list-row .resource > .name > .monaco-icon-label > .actions {
	flex-grow: 100;
}

.scm-view .monaco-list .monaco-list-row .resource-group > .actions,
.scm-view .monaco-list .monaco-list-row .resource > .name > .monaco-icon-label > .actions {
	display: none;
	max-width: fit-content;
}

.scm-view .monaco-list .monaco-list-row:hover .resource-group > .actions,
.scm-view .monaco-list .monaco-list-row.selected .resource-group > .actions,
.scm-view .monaco-list .monaco-list-row.focused .resource-group > .actions,
.scm-view .monaco-list .monaco-list-row:hover .resource > .name > .monaco-icon-label > .actions,
.scm-view .monaco-list .monaco-list-row.selected .resource > .name > .monaco-icon-label > .actions,
.scm-view .monaco-list .monaco-list-row.focused .resource > .name > .monaco-icon-label > .actions,
.scm-view .monaco-list:not(.selection-multiple) .monaco-list-row .resource:hover > .actions {
	display: block;
}

.scm-view .monaco-list .monaco-list-row.force-no-hover,
.scm-view .monaco-list .monaco-list-row:hover.force-no-hover,
.scm-view .monaco-list .monaco-list-row.focused.force-no-hover,
.scm-view .monaco-list .monaco-list-row.selected.force-no-hover {
	background: transparent !important;
}

.scm-view .monaco-list .monaco-list-row.cursor-default {
	cursor: default;
}

.scm-view.show-actions .scm-provider > .actions,
.scm-view.show-actions > .monaco-list .monaco-list-row .resource-group > .actions,
.scm-view.show-actions > .monaco-list .monaco-list-row .resource > .name > .monaco-icon-label > .actions {
	display: block;
}

.scm-view .monaco-list-row .actions .action-label {
	padding: 2px;
}

.scm-view .scm-input {
	height: 100%;
	padding-left: 11px;
	border-radius: 2px;
}

.scm-view .scm-editor-container .monaco-editor {
	border-radius: 2px;
}

.scm-view .scm-editor-container .monaco-editor .focused .selected-text {
	background-color: var(--vscode-editor-selectionBackground);
}

.scm-view .scm-editor {
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.scm-view .button-container {
	display: flex;
	height: 100%;
	padding-left: 11px;
	align-items: center;
}

.scm-view .button-container .codicon.codicon-cloud-upload,
.scm-view .button-container .codicon.codicon-sync {
	margin: 0 4px 0 0;
}

.scm-view .button-container .codicon.codicon-arrow-up,
.scm-view .button-container .codicon.codicon-arrow-down {
	font-size: small !important;
	margin: 0 4px 0 0;
}

.scm-view .button-container > .monaco-button-dropdown {
	flex-grow: 1;
	overflow: hidden;
}

.scm-view .button-container > .monaco-button-dropdown > .monaco-dropdown-button {
	display:flex;
	align-items: center;
	padding: 0 4px;
}


.scm-view .button-container > .monaco-button-dropdown > .monaco-button.monaco-text-button {
	min-width: 0;
}

.scm-view .button-container > .monaco-button-dropdown > .monaco-button.monaco-text-button > span:not(.codicon) {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.scm-view .scm-editor.hidden {
	display: none;
}

.monaco-workbench .part.panel .scm-view .scm-editor-container {
	outline: 1px solid var(--vscode-panelInput-border);
}

.scm-view .scm-editor-container {
	position: relative;
	box-sizing: border-box;
	background-color: var(--vscode-input-background);
	border: 1px solid var(--vscode-input-border, transparent);
	border-radius: 2px;
}

.scm-view .scm-editor-container.synthetic-focus,
.monaco-workbench .part.panel .scm-view .scm-editor-container.synthetic-focus {
	outline: 1px solid var(--vscode-focusBorder);
	outline-offset: -1px;
}

.scm-view .scm-editor-container.validation-info {
	outline: 1px solid var(--vscode-inputValidation-infoBorder) !important;
	outline-offset: -1px;
}

.scm-view .scm-editor-container.validation-warning {
	outline: 1px solid var(--vscode-inputValidation-warningBorder) !important;
	outline-offset: -1px;
}

.scm-view .scm-editor-container.validation-error {
	outline: 1px solid var(--vscode-inputValidation-errorBorder) !important;
	outline-offset: -1px;
}

.scm-editor-validation-container {
	display: flex;
	box-sizing: border-box;
	border-width: 1px;
	border-style: solid;
	border-top: none;
	border-bottom-left-radius: 2px;
	border-bottom-right-radius: 2px;
	padding: 2px;
}

.scm-editor-validation-container.validation-info {
	background-color: var(--vscode-inputValidation-infoBackground);
	border-color: var(--vscode-inputValidation-infoBorder);
	color: var(--vscode-inputValidation-infoForeground);
}

.scm-editor-validation-container.validation-warning {
	background-color: var(--vscode-inputValidation-warningBackground);
	border-color: var(--vscode-inputValidation-warningBorder);
	color: var(--vscode-inputValidation-warningForeground);
}

.scm-editor-validation-container.validation-error {
	background-color: var(--vscode-inputValidation-errorBackground);
	border-color: var(--vscode-inputValidation-errorBorder);
	color: var(--vscode-inputValidation-errorForeground);
}

.scm-editor-validation {
	box-sizing: border-box;
	font-size: 0.9em;
	padding: 1px 3px;
	display: block;
	border-style: none;
	flex: auto;
}

.scm-editor-validation p {
	margin: 0;
	padding: 0;
}

.scm-editor-validation a {
	color: var(--vscode-textLink-foreground);
	-webkit-user-select: none;
	user-select: none;
}

.scm-editor-validation a:active,
.scm-editor-validation a:hover {
	color: var(--vscode-textLink-activeForeground);
}

.scm-editor-validation-actions {
	align-self: start;
	margin-top: 1px;
}

.scm-view .scm-editor-placeholder {
	position: absolute;
	pointer-events: none;
	z-index: 1;
	padding: 2px 6px;
	box-sizing: border-box;
	width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	color: var(--vscode-input-placeholderForeground);
}

.scm-view .scm-editor-placeholder.hidden {
	display: none;
}

.scm-view .scm-editor-container .monaco-editor-background,
.scm-view .scm-editor-container .monaco-editor,
.scm-view .scm-editor-container .monaco-editor .margin,
.monaco-workbench .part.basepanel > .content .scm-view .scm-editor-container .monaco-editor,
.monaco-workbench .part.basepanel > .content .scm-view .scm-editor-container .monaco-editor .margin,
.monaco-workbench .part.basepanel > .content .scm-view .scm-editor-container .monaco-editor .monaco-editor-background {
	color: inherit;
	background-color: var(--vscode-input-background);
}

.scm-view .scm-editor-container .mtk1 {
	color: var(--vscode-input-foreground);
}

/* Repositories */

.scm-repositories-view .scm-provider {
	margin: 0 12px 0 20px;
	overflow: hidden;
}

.scm-repositories-view .scm-provider > .label > .name {
	font-weight: normal;
}
