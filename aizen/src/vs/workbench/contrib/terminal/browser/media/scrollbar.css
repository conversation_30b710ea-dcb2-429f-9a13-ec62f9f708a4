/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .xterm-viewport {
	/* Use the hack presented in https://stackoverflow.com/a/38748186/1156119 to get opacity transitions working on the scrollbar */
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;
	transition: background-color 800ms linear;
}

.monaco-workbench .xterm-viewport {
	scrollbar-width: thin;
}

.monaco-workbench .xterm-viewport::-webkit-scrollbar {
	width: 10px;
}

.monaco-workbench .xterm-viewport::-webkit-scrollbar-track {
	opacity: 0;
}

.monaco-workbench .xterm-viewport::-webkit-scrollbar-thumb {
	min-height: 20px;
	background-color: inherit;
}

.monaco-workbench .force-scrollbar .xterm .xterm-viewport,
.monaco-workbench .xterm.focus .xterm-viewport,
.monaco-workbench .xterm:focus .xterm-viewport,
.monaco-workbench .xterm:hover .xterm-viewport {
	transition: opacity 100ms linear;
	cursor: default;
}

.monaco-workbench .xterm .xterm-viewport::-webkit-scrollbar-thumb:hover {
	transition: opacity 0ms linear;
}

.monaco-workbench .xterm .xterm-viewport::-webkit-scrollbar-thumb:window-inactive {
	background-color: inherit;
}
