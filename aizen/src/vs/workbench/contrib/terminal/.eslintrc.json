{
	"rules": {
		"@typescript-eslint/naming-convention": [
			"warn",
			// variableLike
			{ "selector": "variable", "format": ["camelCase", "UPPER_CASE", "PascalCase"] },
			{ "selector": "variable", "filter": "^I.+Service$", "format": ["PascalCase"], "prefix": ["I"] },
			// memberLike
			{ "selector": "memberLike", "modifiers": ["private"], "format": ["camelCase"], "leadingUnderscore": "require" },
			{ "selector": "memberLike", "modifiers": ["protected"], "format": ["camelCase"], "leadingUnderscore": "require" },
			{ "selector": "enumMember", "format": ["PascalCase"] },
			// memberLike - Allow enum-like objects to use UPPER_CASE
			{ "selector": "method", "modifiers": ["public"], "format": ["camelCase", "UPPER_CASE"] },
			// typeLike
			{ "selector": "typeLike", "format": ["PascalCase"] },
			{ "selector": "interface", "format": ["PascalCase"] }
		]
	}
}
