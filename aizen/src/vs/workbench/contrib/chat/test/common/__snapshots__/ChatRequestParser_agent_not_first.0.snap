{
  parts: [
    {
      range: {
        start: 0,
        endExclusive: 10
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 1,
        endLineNumber: 1,
        endColumn: 11
      },
      text: "Hello Mr. ",
      kind: "text"
    },
    {
      range: {
        start: 10,
        endExclusive: 16
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 11,
        endLineNumber: 1,
        endColumn: 17
      },
      agent: {
        id: "agent",
        metadata: {
          description: "",
          subCommands: [ { name: "subCommand" } ]
        }
      },
      kind: "agent"
    },
    {
      range: {
        start: 16,
        endExclusive: 17
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 17,
        endLineNumber: 1,
        endColumn: 18
      },
      text: " ",
      kind: "text"
    },
    {
      range: {
        start: 17,
        endExclusive: 28
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 18,
        endLineNumber: 1,
        endColumn: 29
      },
      command: { name: "subCommand" },
      kind: "subcommand"
    },
    {
      range: {
        start: 28,
        endExclusive: 35
      },
      editor<PERSON><PERSON><PERSON>: {
        startLineNumber: 1,
        startColumn: 29,
        endLineNumber: 1,
        endColumn: 36
      },
      text: " thanks",
      kind: "text"
    }
  ],
  text: "Hello Mr. @agent /subCommand thanks"
}