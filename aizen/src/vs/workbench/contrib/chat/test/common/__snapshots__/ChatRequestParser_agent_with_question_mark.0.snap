{
  parts: [
    {
      range: {
        start: 0,
        endExclusive: 14
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 1,
        endLineNumber: 1,
        endColumn: 15
      },
      text: "Are you there ",
      kind: "text"
    },
    {
      range: {
        start: 14,
        endExclusive: 20
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 15,
        endLineNumber: 1,
        endColumn: 21
      },
      agent: {
        id: "agent",
        metadata: {
          description: "",
          subCommands: [ { name: "subCommand" } ]
        }
      },
      kind: "agent"
    },
    {
      range: {
        start: 20,
        endExclusive: 21
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 21,
        endLineNumber: 1,
        endColumn: 22
      },
      text: "?",
      kind: "text"
    }
  ],
  text: "Are you there @agent?"
}