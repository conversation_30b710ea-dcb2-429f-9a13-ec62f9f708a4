{
  parts: [
    {
      range: {
        start: 0,
        endExclusive: 6
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 1,
        endLineNumber: 1,
        endColumn: 7
      },
      agent: {
        id: "agent",
        metadata: {
          description: "",
          subCommands: [ { name: "subCommand" } ]
        }
      },
      kind: "agent"
    },
    {
      range: {
        start: 6,
        endExclusive: 18
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 7,
        endLineNumber: 2,
        endColumn: 4
      },
      text: " Please \ndo ",
      kind: "text"
    },
    {
      range: {
        start: 18,
        endExclusive: 29
      },
      editorRange: {
        startLineNumber: 2,
        startColumn: 4,
        endLineNumber: 2,
        endColumn: 15
      },
      command: { name: "subCommand" },
      kind: "subcommand"
    },
    {
      range: {
        start: 29,
        endExclusive: 35
      },
      editorRange: {
        startLineNumber: 2,
        startColumn: 15,
        endLineNumber: 2,
        endColumn: 21
      },
      text: " with ",
      kind: "text"
    },
    {
      range: {
        start: 35,
        endExclusive: 45
      },
      editorRange: {
        startLineNumber: 2,
        startColumn: 21,
        endLineNumber: 2,
        endColumn: 31
      },
      variableName: "selection",
      variableArg: "",
      kind: "var"
    },
    {
      range: {
        start: 45,
        endExclusive: 50
      },
      editorRange: {
        startLineNumber: 2,
        startColumn: 31,
        endLineNumber: 3,
        endColumn: 5
      },
      text: "\nand ",
      kind: "text"
    },
    {
      range: {
        start: 50,
        endExclusive: 63
      },
      editorRange: {
        startLineNumber: 3,
        startColumn: 5,
        endLineNumber: 3,
        endColumn: 18
      },
      variableName: "debugConsole",
      variableArg: "",
      kind: "var"
    }
  ],
  text: "@agent Please \ndo /subCommand with #selection\nand #debugConsole"
}