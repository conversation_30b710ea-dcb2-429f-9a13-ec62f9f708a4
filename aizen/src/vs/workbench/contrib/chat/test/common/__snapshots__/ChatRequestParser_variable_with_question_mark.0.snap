{
  parts: [
    {
      range: {
        start: 0,
        endExclusive: 8
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 1,
        endLineNumber: 1,
        endColumn: 9
      },
      text: "What is ",
      kind: "text"
    },
    {
      range: {
        start: 8,
        endExclusive: 18
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 9,
        endLineNumber: 1,
        endColumn: 19
      },
      variableName: "selection",
      variableArg: "",
      kind: "var"
    },
    {
      range: {
        start: 18,
        endExclusive: 19
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 19,
        endLineNumber: 1,
        endColumn: 20
      },
      text: "?",
      kind: "text"
    }
  ],
  text: "What is #selection?"
}