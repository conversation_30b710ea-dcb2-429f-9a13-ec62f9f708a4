/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .simple-fr-find-part-wrapper {
	overflow: hidden;
	z-index: 10;
	position: absolute;
	top: -45px;
	right: 18px;
	width: var(--notebook-find-width);
	max-width: calc(100% - 28px - 28px - 8px);
	padding: 0 var(--notebook-find-horizontal-padding);
	transition: top 200ms linear;
	visibility: hidden;
	background-color: var(--vscode-editorWidget-background) !important;
	color: var(--vscode-editorWidget-foreground);
	box-shadow: 0 0 8px 2px var(--vscode-widget-shadow);
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
}

.monaco-workbench.reduce-motion .simple-fr-find-part-wrapper {
	transition: top 0ms linear;
}

.monaco-workbench .notebookOverlay .simple-fr-find-part-wrapper.visible {
	z-index: 100;
}

.monaco-workbench .simple-fr-find-part {
	/* visibility: hidden;		Use visibility to maintain flex layout while hidden otherwise interferes with transition */
	z-index: 10;
	position: relative;
	top: 0px;
	display: flex;
	padding: 4px;
	align-items: center;
	pointer-events: all;
	margin: 0 0 0 17px;
}

.monaco-workbench .simple-fr-replace-part {
	/* visibility: hidden;		Use visibility to maintain flex layout while hidden otherwise interferes with transition */
	z-index: 10;
	position: relative;
	top: 0px;
	display: flex;
	padding: 4px;
	align-items: center;
	pointer-events: all;
	margin: 0 0 0 17px;
}

.monaco-workbench .simple-fr-find-part-wrapper .find-replace-progress {
	width: 100%;
	height: 2px;
	position: absolute;
}

.monaco-workbench .simple-fr-find-part-wrapper .find-replace-progress .monaco-progress-container {
	height: 2px;
	top: 0px !important;
	z-index: 100 !important;
}

.monaco-workbench .simple-fr-find-part-wrapper .find-replace-progress .monaco-progress-container .progress-bit {
	height: 2px;
}

.monaco-workbench .simple-fr-find-part-wrapper .monaco-findInput {
	width: 224px;
}

.monaco-workbench .simple-fr-find-part-wrapper .button {
	width: 20px;
	height: 20px;
	flex: initial;
	margin-left: 3px;
	background-position: 50%;
	background-repeat: no-repeat;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
}

.monaco-workbench .simple-fr-find-part-wrapper.visible .simple-fr-find-part {
	visibility: visible;
}

.monaco-workbench .simple-fr-find-part-wrapper .toggle {
	position: absolute;
	top: 0;
	width: 18px;
	height: 100%;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 0px;
	pointer-events: all;
}

.monaco-workbench .simple-fr-find-part-wrapper.visible {
	visibility: visible;
}

.monaco-workbench .simple-fr-find-part-wrapper.visible-transition {
	top: 0;
}

.monaco-workbench .simple-fr-find-part .monaco-findInput {
	flex: 1;
}

.monaco-workbench .simple-fr-find-part .button {
	min-width: 20px;
	width: 20px;
	height: 20px;
	display: flex;
	flex: initial;
	margin-left: 3px;
	background-position: center center;
	background-repeat: no-repeat;
	cursor: pointer;
}

.monaco-workbench .simple-fr-find-part-wrapper .button.disabled {
	opacity: 0.3;
	cursor: default;
}

.monaco-workbench .simple-fr-find-part-wrapper .monaco-custom-toggle.disabled {
	opacity: 0.3;
	cursor: default;
	user-select: none;
	-webkit-user-select: none;
	pointer-events: none;
	background-color: inherit !important;
}

.monaco-workbench .simple-fr-find-part-wrapper .find-filter-button {
	color: inherit;
	margin-left: 2px;
	float: left;
	cursor: pointer;
	box-sizing: border-box;
	user-select: none;
	-webkit-user-select: none;
}

.find-filter-button .monaco-action-bar .action-label {
	padding: 0;
}

.monaco-workbench .simple-fr-find-part .monaco-inputbox > .ibwrapper > .input,
.monaco-workbench .simple-fr-replace-part .monaco-inputbox > .ibwrapper > .input {
	height: 24px;
}
.monaco-workbench .simple-fr-find-part-wrapper .monaco-sash {
	left: 0 !important;
	background-color: var(--vscode-editorWidget-resizeBorder, var(--vscode-editorWidget-border));
}
