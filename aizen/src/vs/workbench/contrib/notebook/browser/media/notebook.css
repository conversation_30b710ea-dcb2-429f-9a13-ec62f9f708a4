/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .notebookOverlay.notebook-editor {
	box-sizing: border-box;
	line-height: 22px;
	user-select: initial;
	-webkit-user-select: initial;
	position: relative;
}

.monaco-workbench .notebookOverlay .cell-list-container > .monaco-list {
	position: absolute;
}

.monaco-workbench .notebookOverlay .cell-list-container .monaco-list-rows {
	min-height: 100%;
	overflow: visible !important;
}

.monaco-workbench .notebookOverlay .cell-list-container .overflowingContentWidgets > div {
	white-space: normal;
}

.monaco-workbench .notebookOverlay .cell-list-container .overflowingContentWidgets > div {
	/* @rebornix: larger than the editor title bar */
	z-index: 600 !important;
}

.monaco-workbench .notebookOverlay .cell-list-container .monaco-editor .overlayWidgets {
	z-index: 638 !important;
}

.monaco-workbench .notebookOverlay .cell-list-container .overflowingContentWidgets > div.parameter-hints-widget {
	z-index: 639 !important;
}

.monaco-workbench .notebookOverlay .cell-list-container .overflowingContentWidgets > div.suggest-widget {
	z-index: 640 !important;
}

.monaco-workbench .notebookOverlay .cell-list-container .overflowingContentWidgets > div .suggest-details-container {
	z-index: 641 !important;
}

.monaco-workbench .notebookOverlay .cell-list-container .monaco-editor .zone-widget.interactive-editor-widget .interactive-editor .markdownMessage {
	white-space: normal;
}

.monaco-workbench .notebookOverlay .cell-list-container {
	position: relative;
}

.monaco-workbench .notebookOverlay.global-drag-active .webview {
	pointer-events: none;
}

.monaco-workbench .notebookOverlay .cell-list-container .webview-cover {
	position: absolute;
	top: 0;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row {
	cursor: default;
	overflow: visible !important;
	width: 100%;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .notebook-gutter > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row {
	cursor: default;
	overflow: visible !important;
	width: 100%;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .cell {
	display: flex;
	position: relative;
}


.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .menu {
	position: absolute;
	left: 0;
	top: 28px;
	visibility: hidden;
	width: 16px;
	margin: auto;
	padding-left: 4px;
}


.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .menu.mouseover,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row:hover .menu,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .cell-output-hover .menu {
	visibility: visible;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row:hover,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .cell-output-hover {
	outline: none !important;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row.focused {
	outline: none !important;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .input-collapse-container {
	display: flex;
	align-items: center;
	position: relative;
	box-sizing: border-box;
	width: 100%;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .input-collapse-container .collapsed-execution-icon {
	line-height: normal;
	margin-left: 6px;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .input-collapse-container .collapsed-execution-icon .codicon-notebook-state-success {
	color: var(--vscode-notebookStatusSuccessIcon-foreground);
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .input-collapse-container .collapsed-execution-icon .codicon-notebook-state-error {
	color: var(--vscode-notebookStatusErrorIcon-foreground);
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .input-collapse-container .cell-collapse-preview {
	padding: 0px 8px;
	display: flex;
	align-items: center;
}
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .input-collapse-container .cell-collapse-preview .monaco-tokenized-source {
	font-size: var(--notebook-cell-input-preview-font-size);
	font-family: var(--notebook-cell-input-preview-font-family);
	cursor: pointer;
	white-space: normal;
	overflow: hidden;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .input-collapse-container .cell-collapse-preview .expandInputIcon {
	padding: 2px;
	border-radius: 5px;
	height: 16px;
	width: 16px;

	cursor: pointer;
	z-index: var(--z-index-notebook-input-collapse-condicon);
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .input-collapse-container .cell-collapse-preview .expandInputIcon:before {
	color: grey;
	font-size: 12px;
	line-height: 16px;
	vertical-align: bottom;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .output-collapse-container {
	cursor: pointer;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .output-collapse-container .expandOutputPlaceholder {
	font-style: italic;
	font-size: var(--notebook-cell-output-font-size);
	font-family: var(--monaco-monospace-font);
	min-height: 24px;
	opacity: 0.7;
	user-select: none;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .output-collapse-container .expandOutputIcon {
	position: relative;
	left: 0px;
	padding: 2px;
	border-radius: 5px;
	vertical-align:middle;
	margin-left: 4px;
	height: 16px;
	width: 16px;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .cell-expand-part-button {
	position: relative;
	left: 0px;
	padding: 2px;
	border-radius: 5px;
	vertical-align: middle;
	margin-left: 4px;
	height: 16px;
	width: 16px;
	z-index: var(--z-index-notebook-cell-expand-part-button);
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .output-collapse-container .expandOutputIcon:before {
	color: grey;
	font-size: 12px;
	line-height: 16px;
	vertical-align: bottom;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .cell-expand-part-button:before {
	color: grey;
	font-size: 12px;
	line-height: 16px;
	vertical-align: bottom;
}

.monaco-workbench.hc-black .notebookOverlay .monaco-list-row.focused .cell-editor-focus .cell-editor-part:before,
.monaco-workbench.hc-light .notebookOverlay .monaco-list-row.focused .cell-editor-focus .cell-editor-part:before {
	outline-style: dashed;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .menu.mouseover,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .menu:hover {
	cursor: pointer;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .run-button-container {
	position: absolute;
	flex-shrink: 0;
	z-index: var(--z-index-run-button-container);
	width: 35px;
	left: -35px;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .run-button-container .monaco-toolbar {
	visibility: hidden;
	height: initial;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .run-button-container .monaco-toolbar .action-item:not(.monaco-dropdown-with-primary) .codicon {
	padding: 6px;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .run-button-container .monaco-toolbar .actions-container {
	justify-content: center;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row:hover .run-button-container .monaco-toolbar,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row.focused .run-button-container .monaco-toolbar,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .cell-run-toolbar-dropdown-active .run-button-container .monaco-toolbar,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .cell-output-hover .run-button-container .monaco-toolbar {
	visibility: visible;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .execution-count-label {
	position: absolute;
	font-size: 10px;
	font-family: var(--monaco-monospace-font);
	white-space: pre;
	box-sizing: border-box;
	opacity: .7;
	width: 35px;
	right: 0px;
	text-align: center;
}

.monaco-workbench .notebookOverlay>.cell-list-container>.monaco-list>.monaco-scrollable-element>.monaco-list-rows>.monaco-list-row .cell-statusbar-hidden .execution-count-label {
	line-height: 15px;
}

.monaco-workbench .notebookOverlay .cell .cell-editor-part {
	position: relative;
}

.monaco-workbench .notebookOverlay .cell .monaco-progress-container {
	top: -3px;

	position: absolute;
	left: 0;
	z-index: var(--z-index-notebook-progress-bar);
	height: 2px;
}

.monaco-workbench .notebookOverlay .cell .monaco-progress-container .progress-bit {
	height: 2px;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list:not(.element-focused):focus:before {
	outline: none !important;
}

.monaco-workbench .notebookOverlay.notebook-editor > .cell-list-container > .monaco-list > .monaco-scrollable-element > .scrollbar.visible {
	z-index: var(--z-index-notebook-scrollbar);
	cursor: default;
}

.monaco-workbench .notebookOverlay .monaco-list-row .cell-editor-part:before {
	z-index: var(--z-index-notebook-cell-editor-outline);
	content: "";
	right: 0px;
	left: 0px;
	top: 0px;
	bottom: 0px;
	outline-offset: -1px;
	display: block;
	position: absolute;
	pointer-events: none;
}

.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row .cell-insertion-indicator-top {
	top: -15px;
}

.monaco-workbench .notebookOverlay > .cell-list-container > .cell-list-insertion-indicator {
	position: absolute;
	height: 2px;
	left: 0px;
	right: 0px;
	opacity: 0;
	z-index: var(--z-index-notebook-list-insertion-indicator);
}

/** Theming */

.monaco-action-bar .action-item.verticalSeparator {
	width: 1px !important;
	height: 16px !important;
	margin: 5px 4px !important;
	cursor: default;
	min-width: 1px !important;
}

.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row .cell-decoration {
	top: -6px;
	position: absolute;
	display: flex;
}


.cell-contributed-items.cell-contributed-items-left {
	margin-left: 4px;
}

.cell-contributed-items.cell-contributed-items-right {
	flex-direction: row-reverse;
}

.monaco-workbench .notebookOverlay .monaco-list:focus-within .monaco-list-row .codicon:not(.suggest-icon) {
	color: inherit;
}

.monaco-workbench .notebookOverlay > .cell-list-container .notebook-overview-ruler-container {
	position: absolute;
	top: 0;
	right: 0;
}

/* high contrast border for multi-select */
.hc-black .notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected:not(.focused) .cell-focus-indicator-top:before, .hc-light .notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected:not(.focused) .cell-focus-indicator-top:before { border-top-style: dotted; }
.hc-black .notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected:not(.focused) .cell-focus-indicator-bottom:before, .hc-light .notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected:not(.focused) .cell-focus-indicator-bottom:before { border-bottom-style: dotted; }
.hc-black .notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected:not(.focused) .cell-inner-container:not(.cell-editor-focus) .cell-focus-indicator-left:before, .hc-light .notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected:not(.focused) .cell-inner-container:not(.cell-editor-focus) .cell-focus-indicator-left:before { border-left-style: dotted; }
.hc-black .notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected:not(.focused) .cell-inner-container:not(.cell-editor-focus) .cell-focus-indicator-right:before, .hc-light .notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected:not(.focused) .cell-inner-container:not(.cell-editor-focus) .cell-focus-indicator-right:before  { border-right-style: dotted; }

/** Notebook Cell Comments */

.cell-comment-container.review-widget {
	border-left: 1px solid var(--vscode-peekView-border); border-right: 1px solid var(--vscode-peekView-border);
}

.cell-comment-container.review-widget > .head {
	border-top: 1px solid var(--vscode-peekView-border);
}

.cell-comment-container.review-widget > .body {
	border-bottom: 1px solid var(--vscode-peekView-border);
}

.cell-comment-container.review-widget {
	background-color: var(--vscode-peekViewResult-background);
}


/** Notebook editor background */
.notebookOverlay .cell-drag-image .cell-editor-container > div {
	background: var(--vscode-editor-background) !important;
}
.notebookOverlay .monaco-list-row .cell-title-toolbar,
.notebookOverlay .monaco-list-row.cell-drag-image,
.notebookOverlay .cell-bottom-toolbar-container .action-item,
.notebookOverlay .cell-list-top-cell-toolbar-container .action-item {
	background-color: var(--vscode-editor-background);
}

.monaco-workbench .notebookOverlay.notebook-editor {
	background-color: var(--vscode-notebook-editorBackground);
}

.notebookOverlay .cell .monaco-editor-background,
.notebookOverlay .cell .margin-view-overlays,
.notebookOverlay .cell .cell-statusbar-container {
	background: var(--vscode-notebook-cellEditorBackground, var(--vscode-editor-background));
}

/** Cell toolbar separator */

.notebookOverlay .monaco-list-row .cell-title-toolbar,
.notebookOverlay .cell-bottom-toolbar-container .action-item,
.notebookOverlay .cell-list-top-cell-toolbar-container .action-item {
	border: solid 1px var(--vscode-notebook-cellToolbarSeparator);
}
.notebookOverlay .monaco-action-bar .action-item.verticalSeparator {
	background-color: var(--vscode-notebook-cellToolbarSeparator);
}
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .input-collapse-container {
	border-bottom: solid 1px var(--vscode-notebook-cellToolbarSeparator);
}

/** Focused cell background */

.notebookOverlay .code-cell-row.focused .cell-focus-indicator,
.notebookOverlay .markdown-cell-row.focused,
.notebookOverlay .code-cell-row.focused .input-collapse-container {
	background-color: var(--vscode-notebook-focusedCellBackground) !important;
}

/** Selected cell background */
.notebookOverlay .monaco-list.selection-multiple .markdown-cell-row.selected,
.notebookOverlay .monaco-list.selection-multiple .markdown-cell-row.selected .cell-focus-indicator-bottom,
.notebookOverlay .monaco-list.selection-multiple .code-cell-row.selected .cell-focus-indicator-top,
.notebookOverlay .monaco-list.selection-multiple .code-cell-row.selected .cell-focus-indicator-left,
.notebookOverlay .monaco-list.selection-multiple .code-cell-row.selected .cell-focus-indicator-right,
.notebookOverlay .monaco-list.selection-multiple .code-cell-row.selected .cell-focus-indicator-bottom {
	background-color: var(--vscode-notebook-selectedCellBackground, inherit) !important;
}

.notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected .cell-focus-indicator-top:before,
.notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected .cell-focus-indicator-bottom:before,
.notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected .cell-inner-container:not(.cell-editor-focus) .cell-focus-indicator-left:before,
.notebookOverlay .monaco-list.selection-multiple:focus-within .monaco-list-row.selected .cell-inner-container:not(.cell-editor-focus) .cell-focus-indicator-right:before {
	border-color: var(--vscode-notebook-inactiveSelectedCellBorder, transparent) !important;
}

/** Cell hover background */
.notebookOverlay .code-cell-row:not(.focused):hover .cell-focus-indicator,
.notebookOverlay .code-cell-row:not(.focused).cell-output-hover .cell-focus-indicator,
.notebookOverlay .markdown-cell-row:not(.focused):hover {
	background-color: var(--vscode-notebook-cellHoverBackground) !important;
}

.notebookOverlay .code-cell-row:not(.focused):hover .input-collapse-container,
.notebookOverlay .code-cell-row:not(.focused).cell-output-hover .input-collapse-container {
	background-color: var(--vscode-notebook-cellHoverBackground);
}

/** Cell symbol higlight */
.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row.code-cell-row.nb-symbolHighlight .cell-focus-indicator,
.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row.markdown-cell-row.nb-symbolHighlight {
	background-color: var(--vscode-notebook-symbolHighlightBackground) !important;
}

/** Cell focused editor border */
.notebookOverlay .monaco-list:focus-within .monaco-list-row.focused .cell-editor-focus .cell-editor-part:before {
	outline: solid 1px var(--vscode-notebook-focusedEditorBorder);
}

/** Cell border color */
.notebookOverlay .cell.markdown h1 { border-color: var(--vscode-notebook-cellBorderColor); }
.notebookOverlay .monaco-list-row .cell-editor-part:before { outline: solid 1px var(--vscode-notebook-cellBorderColor); }

/** Cell status bar */
.monaco-workbench .notebookOverlay .cell-statusbar-container .cell-language-picker:hover,
.monaco-workbench .notebookOverlay .cell-statusbar-container .cell-status-item.cell-status-item-has-command:hover {
	background-color: var(--vscode-notebook-cellStatusBarItemHoverBackground);
}

/** Insert toolbar */
.notebookOverlay > .cell-list-container > .cell-list-insertion-indicator {
	background-color: var(--vscode-notebook-cellInsertionIndicator);
}

/** Scrollbar */
.notebookOverlay .cell-list-container > .monaco-list > .monaco-scrollable-element > .scrollbar > .slider {
	background: var(--vscode-notebookScrollbarSlider-background);
}

.notebookOverlay .cell-list-container > .monaco-list > .monaco-scrollable-element > .scrollbar > .slider:hover {
	background: var(--vscode-notebookScrollbarSlider-hoverBackground);
}

.notebookOverlay .cell-list-container > .monaco-list > .monaco-scrollable-element > .scrollbar > .slider.active {
	background: var(--vscode-notebookScrollbarSlider-activeBackground);
}

/** Cell expand */
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .expandInputIcon:hover,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .expandOutputIcon:hover,
.monaco-workbench .notebookOverlay > .cell-list-container > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row .cell-expand-part-button:hover {
	background-color: var(--vscode-toolbar-hoverBackground);
}

/** Cell insertion/deletion */
.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row.code-cell-row.nb-cell-modified .cell-focus-indicator {
	background-color: var(--vscode-editorGutter-modifiedBackground) !important;
}

.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row.markdown-cell-row.nb-cell-modified {
	background-color: var(--vscode-editorGutter-modifiedBackground) !important;
}

.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row.code-cell-row.nb-cell-added .cell-focus-indicator {
	background-color: var(--vscode-diffEditor-insertedTextBackground) !important;
}

.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row.markdown-cell-row.nb-cell-added {
	background-color: var(--vscode-diffEditor-insertedTextBackground) !important;
}

.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row.code-cell-row.nb-cell-deleted .cell-focus-indicator {
	background-color: var(--vscode-diffEditor-removedTextBackground) !important;
}

.monaco-workbench .notebookOverlay .monaco-list .monaco-list-row.markdown-cell-row.nb-cell-deleted {
	background-color: var(--vscode-diffEditor-removedTextBackground) !important;
}

.monaco-workbench .notebookOverlay .codicon-debug-continue { color: var(--vscode-icon-foreground) !important; }
