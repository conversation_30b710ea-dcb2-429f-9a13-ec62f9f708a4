/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.runtime-extensions-editor .monaco-list .monaco-list-rows > .monaco-list-row.odd:not(:hover):not(.focused) {
	background-color: rgba(130, 130, 130, 0.08);
}

.runtime-extensions-editor .extension {
	display: flex;
	padding-left: 20px;
	padding-right: 20px;
}

.runtime-extensions-editor .extension .desc {
	flex: 1;
	padding: 4px 0;
}

.runtime-extensions-editor .extension .desc .name {
	font-weight: bold;
}

.runtime-extensions-editor .extension .desc .msg .codicon {
	vertical-align: middle;
}

.runtime-extensions-editor .extension .time {
	padding: 4px;
	text-align: right;
}

.runtime-extensions-editor .extension .desc>.msg>span:not(:last-child)::after {
	content: '\2022';
	padding: 0 4px;
	opacity: .8;
}

.runtime-extensions-editor .monaco-action-bar  {
	height: unset;
}

.runtime-extensions-editor .monaco-action-bar .actions-container {
	justify-content: left;
}

.runtime-extensions-editor .extension > .icon-container {
	position: relative;
}

.runtime-extensions-editor .extension > .icon-container > .icon {
	width: 42px;
	height: 42px;
	padding: 10px 14px 10px 0;
	flex-shrink: 0;
	object-fit: contain;
}

.runtime-extensions-editor .extension > .icon-container .extension-remote-badge .codicon {
	color: currentColor;
}

.runtime-extensions-editor .extension > .desc > .header-container {
	display: flex;
	overflow: hidden;
}

.runtime-extensions-editor .extension > .desc > .header-container > .header {
	display: flex;
	align-items: baseline;
	flex-wrap: nowrap;
	overflow: hidden;
	flex: 1;
	min-width: 0;
}

.runtime-extensions-editor .extension > .desc > .header-container > .header > .name {
	font-weight: bold;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.runtime-extensions-editor .extension > .desc > .header-container > .header > .version {
	opacity: 0.85;
	font-size: 80%;
	padding-left: 6px;
	min-width: fit-content;
}
