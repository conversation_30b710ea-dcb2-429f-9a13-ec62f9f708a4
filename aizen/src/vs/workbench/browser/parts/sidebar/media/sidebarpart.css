/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench.nosidebar > .part.sidebar {
	display: none !important;
	visibility: hidden !important;
}

.monaco-workbench .part.sidebar .title-actions .actions-container {
	justify-content: flex-end;
}

.monaco-workbench .part.sidebar .title-actions .action-item {
	margin-right: 4px;
}

.monaco-workbench .part.sidebar > .title > .title-label h2 {
	text-transform: uppercase;
}

.monaco-workbench .viewlet .collapsible.header .title {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.monaco-workbench .viewlet .collapsible.header .actions {
	width: 0; /* not using display: none for keyboard a11y reasons */
}

.monaco-workbench .viewlet .split-view-view:hover > .header .actions,
.monaco-workbench .viewlet .collapsible.header.focused .actions {
	width: initial;
	flex: 1;
}

.monaco-workbench .viewlet .collapsible.header .actions .action-label {
	width: 28px;
	background-size: 16px;
	background-position: center center;
	background-repeat: no-repeat;
	margin-right: 0;
	height: 22px;
}

.monaco-workbench .viewlet .collapsible.header .actions .action-label .label {
	display: none;
}

.monaco-workbench .viewlet .collapsible.header.collapsed .actions {
	display: none;
}

.monaco-workbench .viewlet .collapsible.header .action-label {
	margin-right: 0.2em;
	background-repeat: no-repeat;
	width: 16px;
	height: 16px;
}
