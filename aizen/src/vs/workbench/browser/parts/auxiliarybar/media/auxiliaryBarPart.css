/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench.noauxiliarybar .part.auxiliarybar {
	display: none !important;
	visibility: hidden !important;
}

.monaco-workbench .part.auxiliarybar > .content .monaco-editor,
.monaco-workbench .part.auxiliarybar > .content .monaco-editor .margin,
.monaco-workbench .part.auxiliarybar > .content .monaco-editor .monaco-editor-background {
	background-color: var(--vscode-sideBar-background);
}

.monaco-workbench .part.auxiliarybar > .title > .panel-switcher-container > .monaco-action-bar .action-item:hover .action-label,
.monaco-workbench .part.auxiliarybar > .title > .panel-switcher-container > .monaco-action-bar .action-item:focus .action-label {
	color: var(--vscode-sideBarTitle-foreground) !important;
}

.monaco-workbench .part.auxiliarybar > .title > .panel-switcher-container > .monaco-action-bar .action-item.checked .action-label,
.monaco-workbench .part.auxiliarybar > .title > .panel-switcher-container > .monaco-action-bar .action-item:hover .action-label {
	outline: var(--vscode-contrastActiveBorder, unset) solid 1px !important;
}

.monaco-workbench .part.auxiliarybar > .title > .panel-switcher-container > .monaco-action-bar .action-item:not(.checked):hover .action-label {
	outline: var(--vscode-contrastActiveBorder, unset) dashed 1px !important;
}
