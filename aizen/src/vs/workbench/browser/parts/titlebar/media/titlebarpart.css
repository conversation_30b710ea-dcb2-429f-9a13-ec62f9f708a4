/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/* Part Element */
.monaco-workbench .part.titlebar {
	display: flex;
	flex-direction: row;
}

.monaco-workbench.mac .part.titlebar {
	flex-direction: row-reverse;
}

/* Root Container */
.monaco-workbench .part.titlebar > .titlebar-container {
	box-sizing: border-box;
	overflow: hidden;
	flex-shrink: 1;
	flex-grow: 1;
	align-items: center;
	justify-content: space-between;
	user-select: none;
	-webkit-user-select: none;
	display: flex;
	height: 100%;
	width: 100%;
}

/* Account for zooming */
.monaco-workbench .part.titlebar > .titlebar-container.counter-zoom {
	zoom: calc(1.0 / var(--zoom-factor));
}

/* Platform specific root element */
.monaco-workbench.mac .part.titlebar > .titlebar-container {
	line-height: 22px;
}

.monaco-workbench.web .part.titlebar > .titlebar-container,
.monaco-workbench.windows .part.titlebar > .titlebar-container,
.monaco-workbench.linux .part.titlebar > .titlebar-container {
	line-height: 22px;
	justify-content: left;
}

.monaco-workbench.web.safari .part.titlebar,
.monaco-workbench.web.safari .part.titlebar > .titlebar-container {
	/* Must be scoped to safari due to #148851 */
	/* Is required in safari due to #149476 */
	overflow: visible;
}

/* Draggable region */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-drag-region {
	top: 0;
	left: 0;
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	-webkit-app-region: drag;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left,
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center,
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right {
	display: flex;
	height: 100%;
	align-items: center;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left {
	order: 0;
	width: 20%;
	flex-grow: 2;
	justify-content: flex-start;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center {
	order: 1;
	width: 60%;
	max-width: fit-content;
	min-width: 0px;
	margin: 0 10px;
	/* flex-shrink: 10; */
	justify-content: center;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right {
	order: 2;
	width: 20%;
	min-width: min-content;
	flex-grow: 2;
	justify-content: flex-end;
}



/* Window title text */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title {
	flex: 0 1 auto;
	font-size: 12px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	margin-left: auto;
	margin-right: auto;
}

.monaco-workbench.web .part.titlebar > .titlebar-container > .titlebar-center > .window-title,
.monaco-workbench.windows .part.titlebar > .titlebar-container > .titlebar-center > .window-title,
.monaco-workbench.linux .part.titlebar > .titlebar-container > .titlebar-center > .window-title {
	cursor: default;
}

.monaco-workbench.linux .part.titlebar > .titlebar-container > .titlebar-center > .window-title {
	font-size: inherit;
	/* see #55435 */
}

.monaco-workbench .part.titlebar > .titlebar-container .monaco-toolbar .actions-container {
	gap: 4px;
}

/* Window Title Menu */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center {
	z-index: 2500;
	-webkit-app-region: no-drag;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center.hide {
	visibility: hidden;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item > .action-label {
	color: var(--vscode-titleBar-activeForeground);
}

.monaco-workbench .part.titlebar.inactive > .titlebar-container > .titlebar-center > .window-title > .command-center > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item > .action-label  {
	color: var(--vscode-titleBar-inactiveForeground);
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item > .action-label  {
	color: inherit;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center {
	display: flex;
	align-items: stretch;
	color: var(--vscode-commandCenter-foreground);
	background-color: var(--vscode-commandCenter-background);
	border: 1px solid var(--vscode-commandCenter-border);
	overflow: hidden;
	margin-left: 6px;
	border-top-left-radius: 6px;
	border-bottom-left-radius: 6px;
	border-top-right-radius: 6px;
	border-bottom-right-radius: 6px;
	height: 22px;
	width: 38vw;
	max-width: 600px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center .action-item.command-center-quick-pick {
	display: flex;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center .action-item.command-center-quick-pick .search-icon {
	font-size: 14px;
	opacity: .8;
	margin: auto 3px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center .action-item.command-center-quick-pick .search-label {
	overflow: hidden;
	text-overflow: ellipsis;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center.multiple  {
	justify-content: flex-start;
	padding: 0 12px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center.multiple.active .action-label {
	background-color: inherit;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center:only-child {
	margin-left: 0; /* no margin if there is only the command center, without nav buttons */
}

.monaco-workbench .part.titlebar.inactive > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center {
	color: var(--vscode-titleBar-inactiveForeground);
	border-color: var(--vscode-commandCenter-inactiveBorder) !important;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center:HOVER {
	color: var(--vscode-commandCenter-activeForeground);
	background-color: var(--vscode-commandCenter-activeBackground);
	border-color: var(--vscode-commandCenter-activeBorder);
}

/* Menubar */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left > .menubar {
	/* move menubar above drag region as negative z-index on drag region cause greyscale AA */
	z-index: 2500;
	min-width: 36px;
	flex-wrap: nowrap;
	order: 2;
}

.monaco-workbench.web .part.titlebar > .titlebar-container > .titlebar-left > .menubar {
	margin-left: 4px;
}

.monaco-workbench .part.titlebar > .titlebar-container.counter-zoom .menubar .menubar-menu-button > .menubar-menu-items-holder.monaco-menu-container {
	zoom: var(--zoom-factor);
}

/* Resizer */
.monaco-workbench.windows .part.titlebar > .titlebar-container > .resizer,
.monaco-workbench.linux .part.titlebar > .titlebar-container > .resizer {
	-webkit-app-region: no-drag;
	position: absolute;
	top: 0;
	width: 100%;
	height: 4px;
}

.monaco-workbench.windows.fullscreen .part.titlebar > .titlebar-container > .resizer,
.monaco-workbench.linux.fullscreen .part.titlebar > .titlebar-container > .resizer {
	display: none;
}

/* App Icon */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left > .window-appicon {
	width: 35px;
	height: 100%;
	position: relative;
	z-index: 2500;
	flex-shrink: 0;
	order: 1;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left > .window-appicon:not(.codicon) {
	background-image: url('../../../media/code-icon.svg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 16px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left > .window-appicon.codicon {
	line-height: 30px;
}

.monaco-workbench.fullscreen .part.titlebar > .titlebar-container > .titlebar-left > .window-appicon {
	display: none;
}

.monaco-workbench .part.titlebar > .titlebar-container .window-appicon > .home-bar-icon-badge {
	position: absolute;
	right: 9px;
	bottom: 6px;
	width: 8px;
	height: 8px;
	z-index: 1;
	/* on top of home indicator */
	background-image: url('../../../media/code-icon.svg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 8px;
	pointer-events: none;
	border-top: 1px solid transparent;
	border-left: 1px solid transparent;
}

/* Window Controls (Minimize, Max/Restore, Close) */
.monaco-workbench .part.titlebar .window-controls-container {
	display: flex;
	flex-grow: 0;
	flex-shrink: 0;
	text-align: center;
	z-index: 3000;
	-webkit-app-region: no-drag;
	width: 0px;
	height: 100%;
}

/* Web WCO Sizing/Ordering */
.monaco-workbench.web .part.titlebar .titlebar-right .window-controls-container {
	width: calc(100vw - env(titlebar-area-width, 100vw) - env(titlebar-area-x, 0px));
	height: env(titlebar-area-height, 35px);
}

.monaco-workbench.web .part.titlebar .titlebar-left .window-controls-container {
	width: env(titlebar-area-x, 0px);
	height: env(titlebar-area-height, 35px);
}

.monaco-workbench.web.mac .part.titlebar .titlebar-left .window-controls-container {
	order: 0;
}

.monaco-workbench.web.mac .part.titlebar .titlebar-right .window-controls-container {
	order: 1;
}

/* Desktop Windows/Linux Window Controls*/
.monaco-workbench:not(.web):not(.mac) .part.titlebar .window-controls-container.primary {
	width: calc(138px / var(--zoom-factor, 1));
}

.monaco-workbench:not(.web):not(.mac) .part.titlebar .titlebar-container.counter-zoom .window-controls-container.primary {
	width: 138px;
}

.monaco-workbench:not(.web):not(.mac) .part.titlebar .titlebar-container:not(.counter-zoom) .window-controls-container * {
	zoom: calc(1 / var(--zoom-factor, 1));
}

/* Desktop macOS Window Controls */
.monaco-workbench:not(.web).mac .part.titlebar .window-controls-container.primary {
	width: 70px;
}

.monaco-workbench.fullscreen .part.titlebar .window-controls-container {
	display: none;
	background-color: transparent;
}

/* Window Control Icons */
.monaco-workbench .part.titlebar .window-controls-container > .window-icon {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	width: 46px;
	font-size: 16px;
}

.monaco-workbench .part.titlebar .window-controls-container > .window-icon::before {
	height: 16px;
	line-height: 16px;
}

.monaco-workbench .part.titlebar .window-controls-container > .window-icon:hover {
	background-color: rgba(255, 255, 255, 0.1);
}

.monaco-workbench .part.titlebar.light > .window-controls-container > .window-icon:hover {
	background-color: rgba(0, 0, 0, 0.1);
}

.monaco-workbench .part.titlebar .window-controls-container > .window-icon.window-close:hover {
	background-color: rgba(232, 17, 35, 0.9);
}

.monaco-workbench .part.titlebar .window-controls-container .window-icon.window-close:hover {
	color: white;
}

/* Layout Controls */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .layout-controls-container {
	display: none;
	padding-right: 2px;
	flex-grow: 0;
	flex-shrink: 0;
	text-align: center;
	position: relative;
	z-index: 2500;
	-webkit-app-region: no-drag;
	height: 100%;
	margin-left: auto;
	min-width: 28px;
}

.monaco-workbench.mac:not(.web) .part.titlebar > .titlebar-container > .titlebar-right > .layout-controls-container {
	right: 8px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .layout-controls-container.show-layout-control {
	display: flex;
	justify-content: center;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .layout-controls-container .codicon {
	color: inherit;
}

.monaco-workbench .part.titlebar .window-controls-container .window-icon {
	color: var(--vscode-titleBar-activeForeground);
}

.monaco-workbench .part.titlebar.inactive .window-controls-container .window-icon {
	color: var(--vscode-titleBar-inactiveForeground);
}
