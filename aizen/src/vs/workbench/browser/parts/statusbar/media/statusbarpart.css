/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .part.statusbar {
	box-sizing: border-box;
	cursor: default;
	width: 100%;
	height: 22px;
	font-size: 12px;
	display: flex;
	overflow: hidden;
}

.monaco-workbench:not(.reduce-motion) .part.statusbar {
	transition: background-color 0.15s ease-out;
}

.monaco-workbench .part.statusbar.status-border-top::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	z-index: 5;
	pointer-events: none;
	background-color: var(--status-border-top-color);
	width: 100%;
	height: 1px;
}

.monaco-workbench .part.statusbar > .left-items,
.monaco-workbench .part.statusbar > .right-items {
	display: flex;
}

.monaco-workbench .part.statusbar > .right-items {
	flex-wrap: wrap; /* overflow elements by wrapping */
	flex-direction: row-reverse; /* let the elements to the left wrap first */
}

.monaco-workbench .part.statusbar > .left-items {
	flex-grow: 1; /* left items push right items to the far right end */
}

.monaco-workbench  .part.statusbar > .items-container > .statusbar-item {
	display: inline-block;
	line-height: 22px;
	height: 100%;
	vertical-align: top;
	max-width: 40vw;
	font-variant-numeric: tabular-nums;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.has-beak {
	position: relative;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.has-beak > .status-bar-item-beak-container {
	position: absolute;
	left: calc(50% - 5px); /* centering relative to parent */
	top: -5px;
	width: 10px;
	height: 5px;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.has-beak > .status-bar-item-beak-container:before {
	content: '';
	position: fixed;
	border-bottom-width: 5px;
	border-bottom-style: solid;
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item > .statusbar-item-label {
	margin-right: 3px;
	margin-left: 3px;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.compact-left > .statusbar-item-label {
	margin-right: 3px;
	margin-left: 0;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.compact-right > .statusbar-item-label {
	margin-right: 0;
	margin-left: 3px;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.left.first-visible-item {
	padding-left: 7px; /* Add padding to the most left status bar item */
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.right.last-visible-item {
	margin-right: 7px; /* Add margin to the most right status bar item */
}

/* Tweak appearance for items with background to improve hover feedback */
.monaco-workbench .part.statusbar > .items-container > .statusbar-item.has-background-color.left.first-visible-item,
.monaco-workbench .part.statusbar > .items-container > .statusbar-item.has-background-color.right.last-visible-item {
	padding-right: 0;
	padding-left: 0;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.has-background-color > .statusbar-item-label {
	margin-right: 0;
	margin-left: 0;
	padding-left: 10px;
	padding-right: 10px;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.compact-left.has-background-color > .statusbar-item-label {
	padding-left: 3px;
	padding-right: 10px;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.compact-right.has-background-color > .statusbar-item-label {
	padding-left: 10px;
	padding-right: 3px;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item > .statusbar-item-label {
	cursor: pointer;
	display: flex;
	height: 100%;
	padding: 0 5px 0 5px;
	white-space: pre; /* gives some degree of styling */
	align-items: center;
	text-overflow: ellipsis;
	overflow: hidden;
	outline-width: 0px; /* do not render focus outline, we already have background */
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.compact-left > .statusbar-item-label {
	padding: 0 3px;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.compact-right > .statusbar-item-label {
	padding: 0 3px;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item > a:hover:not(.disabled) {
	text-decoration: none;
	color: var(--vscode-statusBarItem-hoverForeground);
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item > a.disabled {
	cursor: default;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item  span.codicon {
	text-align: center;
	font-size: 14px;
	color: inherit;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item a:active:not(.disabled) {
	outline: 1px solid var(--vscode-contrastActiveBorder) !important;
	outline-offset: -1px;
}

.monaco-workbench:not(.hc-light):not(.hc-black) .part.statusbar > .items-container > .statusbar-item a:active:not(.disabled) {
	background-color: var(--vscode-statusBarItem-activeBackground) !important;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item a:hover:not(.disabled) {
	outline: 1px dashed var(--vscode-contrastActiveBorder);
	outline-offset: -1px;
}

.monaco-workbench:not(.hc-light):not(.hc-black) .part.statusbar > .items-container > .statusbar-item a:hover:not(.disabled) {
	background-color: var(--vscode-statusBarItem-hoverBackground);
}

/** Status bar entry item kinds */

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.warning-kind {
	color: var(--vscode-statusBarItem-warningForeground);
	background-color: var(--vscode-statusBarItem-warningBackground);
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.warning-kind a:hover:not(.disabled) {
	color: var(--vscode-statusBarItem-warningHoverForeground);
	background-color: var(--vscode-statusBarItem-warningHoverBackground) !important;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.error-kind {
	color: var(--vscode-statusBarItem-errorForeground);
	background-color: var(--vscode-statusBarItem-errorBackground);
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.error-kind a:hover:not(.disabled) {
	color: var(--vscode-statusBarItem-errorHoverForeground);
	background-color: var(--vscode-statusBarItem-errorHoverBackground) !important;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.prominent-kind {
	color: var(--vscode-statusBarItem-prominentForeground);
	background-color: var(--vscode-statusBarItem-prominentBackground);
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.prominent-kind a:hover:not(.disabled) {
	color: var(--vscode-statusBarItem-prominentHoverForeground);
	background-color: var(--vscode-statusBarItem-prominentHoverBackground) !important;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.remote-kind {
	color: var(--vscode-statusBarItem-remoteForeground);
	background-color: var(--vscode-statusBarItem-remoteBackground);
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.remote-kind a:hover:not(.disabled) {
	color: var(--vscode-statusBarItem-remoteHoverForeground);
	background-color: var(--vscode-statusBarItem-remoteHoverBackground) !important;
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.offline-kind {
	color: var(--vscode-statusBarItem-offlineForeground);
	background-color: var(--vscode-statusBarItem-offlineBackground);
}

.monaco-workbench .part.statusbar > .items-container > .statusbar-item.offline-kind a:hover:not(.disabled) {
	color: var(--vscode-statusBarItem-offlineHoverForeground);
	background-color: var(--vscode-statusBarItem-offlineHoverBackground) !important;
}
