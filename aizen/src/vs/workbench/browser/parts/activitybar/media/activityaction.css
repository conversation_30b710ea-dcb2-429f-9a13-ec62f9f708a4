/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item {
	display: block;
	position: relative;
}

.monaco-workbench .activitybar > .content .composite-bar > .monaco-action-bar .action-item::before,
.monaco-workbench .activitybar > .content .composite-bar > .monaco-action-bar .action-item::after {
	position: absolute;
	content: '';
	width: 48px;
	height: 2px;
	display: none;
	background-color: transparent;
	transition-property: background-color;
	transition-duration: 0ms;
	transition-delay: 100ms;
}

.monaco-workbench .activitybar > .content.dragged-over .composite-bar > .monaco-action-bar .action-item::before,
.monaco-workbench .activitybar > .content.dragged-over .composite-bar > .monaco-action-bar .action-item::after {
	display: block;
}

.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item::before {
	top: 1px;
	margin-top: -2px;
}

.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item::after {
	bottom: 1px;
	margin-bottom: -2px;
}

.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item:first-of-type::before {
	top: 2px;
	margin-top: -2px;
}

.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item:last-of-type::after {
	bottom: 2px;
	margin-bottom: -2px;
}

.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item.top::before,
.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item.top::after,
.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item.bottom::before,
.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item.bottom::after {
	transition-delay: 0s;
}

.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item.bottom + .action-item::before,
.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item.top::before,
.monaco-workbench .activitybar > .content > .composite-bar > .monaco-action-bar .action-item:last-of-type.bottom::after,
.monaco-workbench .activitybar > .content.dragged-over-head > .composite-bar > .monaco-action-bar .action-item:first-of-type::before,
.monaco-workbench .activitybar > .content.dragged-over-tail > .composite-bar > .monaco-action-bar .action-item:last-of-type::after {
	background-color: var(--insert-border-color);
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-label {
	position: relative;
	z-index: 1;
	display: flex;
	overflow: hidden;
	width: 48px;
	height: 48px;
	margin-right: 0;
	box-sizing: border-box;

}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-label:not(.codicon) {
	font-size: 15px;
	line-height: 40px;
	padding: 0 0 0 48px;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-label.codicon {
	font-size: 24px;
	align-items: center;
	justify-content: center;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item.active .action-label.codicon,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:focus .action-label.codicon,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:hover .action-label.codicon {
	color: var(--vscode-activityBar-foreground) !important;
}
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item.active .action-label.uri-icon,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:focus .action-label.uri-icon,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:hover .action-label.uri-icon {
	background-color: var(--vscode-activityBar-foreground) !important;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item.checked .active-item-indicator:before,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:focus .active-item-indicator:before {
	content: "";
	position: absolute;
	z-index: 1;
	top: 0;
	height: 100%;
	width: 0;
	border-left: 2px solid;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item.checked .active-item-indicator:before {
	top: 0;
	height: 100%;
}


/* Hides active elements in high contrast mode */
.monaco-workbench.hc-black .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item.checked:not(:focus) .active-item-indicator.action-item,
.monaco-workbench.hc-light .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item.checked:not(:focus) .active-item-indicator {
	display: none;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item.clicked:focus:before {
	border-left: none !important; /* no focus feedback when using mouse */
}

.monaco-workbench .activitybar.left > .content :not(.monaco-menu) > .monaco-action-bar .action-item .active-item-indicator:before{
	left: 0;
}

.monaco-workbench .activitybar.right > .content :not(.monaco-menu) > .monaco-action-bar .action-item .active-item-indicator:before {
	right: 0;
}

/* Hides outline on HC as focus is handled by border */
.monaco-workbench.hc-black .activitybar.left > .content :not(.monaco-menu) > .monaco-action-bar .action-item:focus:before,
.monaco-workbench.hc-black .activitybar.right > .content :not(.monaco-menu) > .monaco-action-bar .action-item:focus:before,
.monaco-workbench.hc-light .activitybar.left > .content :not(.monaco-menu) > .monaco-action-bar .action-item:focus:before,
.monaco-workbench.hc-light .activitybar.right > .content :not(.monaco-menu) > .monaco-action-bar .action-item:focus:before {
	outline: none;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .profile-badge,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .active-item-indicator,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .badge {
	position: absolute;
	top: 0;
	bottom: 0;
	margin: auto;
	left: 0;
	overflow: hidden;
	width: 100%;
	height: 100%;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .active-item-indicator,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .badge {
	z-index: 2;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .profile-badge {
	z-index: 1;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .active-item-indicator {
	pointer-events: none;
}

.monaco-workbench.border .activitybar.right > .content :not(.monaco-menu) > .monaco-action-bar .active-item-indicator {
	left: -2px;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .badge .badge-content {
	position: absolute;
	top: 24px;
	right: 8px;
	font-size: 9px;
	font-weight: 600;
	min-width: 8px;
	height: 16px;
	line-height: 16px;
	padding: 0 4px;
	border-radius: 20px;
	text-align: center;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .profile-badge .profile-icon-overlay {
	position: absolute;
	top: 27px;
	right: 6px;
	background-color: var(--vscode-activityBar-background);
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .profile-badge .profile-icon-overlay .codicon {
	color: var(--vscode-activityBar-inactiveForeground);
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item.active .profile-badge .profile-icon-overlay .codicon,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:focus .profile-badge .profile-icon-overlay .codicon,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:hover .profile-badge .profile-icon-overlay .codicon {
	color: var(--vscode-activityBar-foreground) !important;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .profile-badge .profile-text-overlay {
	position: absolute;
	font-weight: 600;
	font-size: 9px;
	line-height: 10px;
	top: 24px;
	right: 6px;
	padding: 2px 3px;
	border-radius: 7px;
	background-color: var(--vscode-profileBadge-background);
	color: var(--vscode-profileBadge-foreground);
	border: 2px solid var(--vscode-activityBar-background);
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:active .profile-text-overlay,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:focus .profile-text-overlay,
.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .action-item:hover .profile-text-overlay {
	color: var(--vscode-activityBar-foreground);
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .badge .codicon.badge-content {
	font-size: 12px;
	font-weight: unset;
	padding: 0;
	justify-content: center;
}

.monaco-workbench .activitybar > .content :not(.monaco-menu) > .monaco-action-bar .badge .codicon.badge-content::before {
	text-align: center;
	vertical-align: baseline;
}

/* Right aligned */

.monaco-workbench .activitybar.right>.content :not(.monaco-menu)>.monaco-action-bar .profile-badge,
.monaco-workbench .activitybar.right > .content :not(.monaco-menu) > .monaco-action-bar .badge {
	left: auto;
	right: 0;
}
