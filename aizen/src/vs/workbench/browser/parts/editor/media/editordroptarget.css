/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

#monaco-workbench-editor-drop-overlay {
	position: absolute;
	z-index: 10000;
	width: 100%;
	height: 100%;
	left: 0;
}

#monaco-workbench-editor-drop-overlay > .editor-group-overlay-indicator {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none; /* very important to not take events away from the parent */
	opacity: 0; /* hidden initially */

	display: flex;
	align-items: center;
	justify-content: center;
}

.monaco-workbench:not(.reduce-motion) #monaco-workbench-editor-drop-overlay > .editor-group-overlay-indicator {
	transition: opacity 150ms ease-out;
}

#monaco-workbench-editor-drop-overlay .editor-group-overlay-drop-into-prompt {
	text-align: center;
	padding: 0.6em;
	margin: 0.2em;
	line-height: normal;
	opacity: 0; /* hidden initially */
}

.monaco-workbench:not(.reduce-motion) #monaco-workbench-editor-drop-overlay .editor-group-overlay-drop-into-prompt {
	transition: opacity 150ms ease-out;
}

#monaco-workbench-editor-drop-overlay .editor-group-overlay-drop-into-prompt i /* Style keybinding */ {
	padding: 0 8px;
	border: 1px solid hsla(0,0%,80%,.4);
	margin: 0 1px;
	border-radius: 5px;
	background-color: rgba(255, 255, 255, 0.05);
	font-style: normal;
}

.monaco-workbench:not(.reduce-motion) #monaco-workbench-editor-drop-overlay > .editor-group-overlay-indicator.overlay-move-transition {
	transition: top 70ms ease-out, left 70ms ease-out, width 70ms ease-out, height 70ms ease-out, opacity 150ms ease-out;
}
