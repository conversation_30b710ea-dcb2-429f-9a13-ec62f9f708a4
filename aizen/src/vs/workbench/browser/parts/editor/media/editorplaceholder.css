/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor-pane-placeholder {
	padding: 0 16px 0 16px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 10px;
}

.monaco-editor-pane-placeholder:focus {
	outline: none !important;
}

.monaco-editor-pane-placeholder .editor-placeholder-icon-container .codicon {
	font-size: 48px;
}

.monaco-editor-pane-placeholder .editor-placeholder-icon-container .codicon.codicon-error {
	color: var(--vscode-editorError-foreground);
}

.monaco-editor-pane-placeholder .editor-placeholder-icon-container .codicon.codicon-warning {
	color: var(--vscode-editorWarning-foreground);
}

.monaco-editor-pane-placeholder .editor-placeholder-icon-container .codicon.codicon-info,
.monaco-editor-pane-placeholder .editor-placeholder-icon-container .codicon.codicon-workspace-untrusted {
	color: var(--vscode-editorInfo-foreground);
}

.monaco-editor-pane-placeholder.max-height-200px .editor-placeholder-icon-container {
	/* Hide the icon when height is limited */
	display: none;
}

.monaco-editor-pane-placeholder .editor-placeholder-label-container {
	font-size: 14px;
	max-width: 450px;
	text-align: center;
	word-break: break-word;
	user-select: text;
	-webkit-user-select: text;
}

.monaco-editor-pane-placeholder .editor-placeholder-buttons-container {
	display: flex;
}

.monaco-editor-pane-placeholder .editor-placeholder-buttons-container > .monaco-button {
	margin: 4px 5px;
}

.monaco-editor-pane-placeholder .editor-placeholder-buttons-container > .monaco-button {
	font-size: 14px;
	width: fit-content;
	padding: 6px 11px;
	outline-offset: 2px !important;
}
