/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/* Title Label */

.monaco-workbench .part.editor > .content .editor-group-container > .title > .label-container {
	height: var(--editor-group-tab-height);
	display: flex;
	justify-content: flex-start;
	align-items: center;
	overflow: hidden;
	flex: auto;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title > .label-container > .title-label {
	line-height: var(--editor-group-tab-height);
	overflow: hidden;
	text-overflow: ellipsis;
	position: relative;
	padding-left: 20px;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title > .label-container > .title-label > .monaco-icon-label-container {
	flex: initial; /* helps to show decorations right next to the label and not at the end while still preserving text overflow ellipsis */
}

/* Breadcrumbs (inline next to single editor tab) */

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .no-tabs.title-label {
	flex: none;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control {
	flex: 1 50%;
	overflow: hidden;
	margin-left: .45em;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-breadcrumb-item {
	font-size: 0.9em;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control.preview .monaco-breadcrumb-item {
	font-style: italic;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-breadcrumb-item::before {
	content: '/';
	opacity: 1;
	height: inherit;
	width: inherit;
	background-image: none;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control.backslash-path .monaco-breadcrumb-item::before  {
	content: '\\';
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-breadcrumb-item.root_folder::before,
.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-breadcrumb-item.root_folder + .monaco-breadcrumb-item::before,
.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control.relative-path .monaco-breadcrumb-item:nth-child(2)::before,
.monaco-workbench.windows .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-breadcrumb-item:nth-child(2)::before {
	display: none; /* workspace folder, item following workspace folder, or relative path -> hide first seperator */
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-breadcrumb-item.root_folder::after {
	content: '\00a0•\00a0'; /* use dot separator for workspace folder */
	padding: 0;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-breadcrumb-item:last-child {
	padding-right: 4px; /* does not have trailing separator*/
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-breadcrumb-item .codicon[class*='codicon-symbol-'] {
	padding: 0 1px;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-breadcrumb-item .codicon:last-child {
	display: none; /* hides chevrons when no tabs visible */
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.breadcrumbs .breadcrumbs-control .monaco-icon-label::before {
	height: 18px;
	padding-right: 2px;
}

/* Editor Actions Toolbar (via title actions) */

.monaco-workbench .part.editor > .content .editor-group-container > .title > .title-actions {
	display: flex;
	flex: initial;
	opacity: 0.5;
	padding-right: 8px;
	height: var(--editor-group-tab-height);
}

.monaco-workbench .part.editor > .content .editor-group-container > .title > .title-actions .action-item {
	margin-right: 4px;
}

.monaco-workbench .part.editor > .content .editor-group-container.active > .title > .title-actions {
	opacity: 1;
}
