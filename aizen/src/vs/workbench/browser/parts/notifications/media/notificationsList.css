/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/** Notification: Container */

.monaco-workbench .notifications-list-container {
	color: var(--vscode-notifications-foreground);
	background: var(--vscode-notifications-background);
	outline-color: var(--vscode-contrastBorder);
	border-radius: inherit;
}

.monaco-workbench .notifications-list-container .notification-list-item {
	display: flex;
	flex-direction: column;
	flex-direction: column-reverse; /* the details row appears first in order for better keyboard access to notification buttons */
	padding: 10px 5px;
	height: 100%;
	box-sizing: border-box;
	border-radius: 4px;
}

.monaco-workbench .notifications-list-container .notification-offset-helper {
	opacity: 0;
	position: absolute;
	line-height: 22px;
	word-wrap: break-word; /* never overflow long words, but break to next line */
}

.monaco-workbench .notifications-list-container .monaco-scrollable-element {
	border-radius: 4px;
}

/** Notification: Main Row */

.monaco-workbench .notifications-list-container .notification-list-item > .notification-list-item-main-row {
	display: flex;
	flex-grow: 1;
}

/** Notification: Icon */

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-icon {
	display: flex;
	align-items: center;
	flex: 0 0 16px;
	height: 22px;
	margin-right: 4px;
	margin-left: 4px;
	font-size: 18px;
	background-position: center;
	background-repeat: no-repeat;
}

/** Notification: Message */

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-message {
	line-height: 22px;
	overflow: hidden;
	text-overflow: ellipsis;
	flex: 1; /* let the message always grow */
	user-select: text;
	-webkit-user-select: text;
}

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-message a {
	color: var(--vscode-notificationLink-foreground);
}

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-message a:focus {
	outline-width: 1px;
	outline-style: solid;
	outline-color: var(--vscode-focusBorder);
}

.monaco-workbench .notifications-list-container .notification-list-item.expanded .notification-list-item-message {
	white-space: normal;
	word-wrap: break-word; /* never overflow long words, but break to next line */
}

/** Notification: Toolbar Container */

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-toolbar-container {
	display: none;
	height: 22px;
}

.monaco-workbench .notifications-list-container .monaco-list:focus-within .notification-list-item .notification-list-item-toolbar-container,
.monaco-workbench .notifications-list-container .notification-list-item:hover .notification-list-item-toolbar-container,
.monaco-workbench .notifications-list-container .monaco-list-row.focused .notification-list-item .notification-list-item-toolbar-container,
.monaco-workbench .notifications-list-container .notification-list-item.expanded .notification-list-item-toolbar-container {
	display: block;
}

/** Notification: Details Row */

.monaco-workbench .notifications-list-container .notification-list-item > .notification-list-item-details-row {
	display: none;
	align-items: center;
	padding-left: 5px;
	overflow: hidden; /* details row should never overflow */
}

.monaco-workbench .notifications-list-container .notification-list-item.expanded > .notification-list-item-details-row {
	display: flex;
}

/** Notification: Source */

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-source {
	flex: 1;
	font-size: 12px;
	overflow: hidden; /* always give away space to buttons container */
	text-overflow: ellipsis;
}

/** Notification: Buttons */

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-buttons-container {
	display: flex;
	overflow: hidden;
}

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-buttons-container > .monaco-button-dropdown,
.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-buttons-container > .monaco-button {
	margin: 4px 5px; /* allows button focus outline to be visible */
}

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-buttons-container .monaco-button {
	outline-offset: 2px !important;
}

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-buttons-container .monaco-text-button {
	width: fit-content;
	padding: 4px 10px;
	display: inline-block;	/* to enable ellipsis in text overflow */
	font-size: 12px;
	overflow: hidden;
	text-overflow: ellipsis;
}

.monaco-workbench .notifications-list-container .notification-list-item .notification-list-item-buttons-container .monaco-dropdown-button {
	padding: 5px
}

/** Notification: Progress */

.monaco-workbench .notifications-list-container .progress-bit {
	height: 2px;
	bottom: 0;
}
