name: @@NAME@@
version: '@@VERSION@@'
summary: Code editing. Redefined.
description: |
  Visual Studio Code is a new choice of tool that combines the
  simplicity of a code editor with what developers need for the core
  edit-build-debug cycle.

architectures:
  - build-on: amd64
    run-on: @@ARCHITECTURE@@

grade: stable
confinement: classic
base: core20
compression: lzo

parts:
  code:
    plugin: dump
    source: .
    stage-packages:
      - ca-certificates
      - libasound2
      - libatk-bridge2.0-0
      - libatk1.0-0
      - libatspi2.0-0
      - libcairo2
      - libcanberra-gtk3-module
      - libcurl3-gnutls
      - libcurl3-nss
      - libcurl4
      - libdrm2
      - libgbm1
      - libgl1
      - libglib2.0-0
      - libgtk-3-0
      - libibus-1.0-5
      - libnss3
      - libpango-1.0-0
      - libsecret-1-0
      - libxcomposite1
      - libxdamage1
      - libxfixes3
      - libxkbcommon0
      - libxkbfile1
      - libxrandr2
      - libxss1
      - locales-all
      - packagekit-gtk3-module
      - xdg-utils
    prime:
      - -usr/share/doc
      - -usr/share/fonts
      - -usr/share/icons
      - -usr/share/lintian
      - -usr/share/man
    override-build: |
      snapcraftctl build
      patchelf --force-rpath --set-rpath '$ORIGIN/../../lib/x86_64-linux-gnu:$ORIGIN:/snap/core20/current/lib/x86_64-linux-gnu' $SNAPCRAFT_PART_INSTALL/usr/share/@@NAME@@/chrome_crashpad_handler
  cleanup:
    after:
      - code
    plugin: nil
    build-snaps:
      - core20
    override-prime: |
      set -eux
      for snap in "core20"; do
        cd "/snap/$snap/current" && find . -type f,l -exec rm -f "$SNAPCRAFT_PRIME/{}" \;
      done
      patchelf --print-rpath $SNAPCRAFT_PRIME/usr/share/@@NAME@@/chrome_crashpad_handler


apps:
  @@NAME@@:
    command: electron-launch $SNAP/usr/share/@@NAME@@/bin/@@NAME@@ --no-sandbox
    common-id: @@NAME@@.desktop

  url-handler:
    command: electron-launch $SNAP/usr/share/@@NAME@@/bin/@@NAME@@ --open-url --no-sandbox
