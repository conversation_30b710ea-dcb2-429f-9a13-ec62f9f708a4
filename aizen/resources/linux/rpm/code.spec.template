Name:     @@NAME@@
Version:  @@VERSION@@
Release:  @@RELEASE@@.el7
Summary:  Code editing. Redefined.
Group:    Development/Tools
Vendor:   Microsoft Corporation
Packager: Visual Studio Code Team <<EMAIL>>
License:  @@LICENSE@@
URL:      https://code.visualstudio.com/
Icon:     @@NAME@@.xpm
Requires: @@DEPENDENCIES@@
AutoReq:  0

%global __provides_exclude_from ^%{_datadir}/%{name}/.*\\.so.*$

%description
Visual Studio Code is a new choice of tool that combines the simplicity of a code editor with what developers need for the core edit-build-debug cycle. See https://code.visualstudio.com/docs/setup/linux for installation instructions and FAQ.

# Don't generate build_id links to prevent conflicts when installing multiple
# versions of VS Code alongside each other (e.g. `code` and `code-insiders`)
%define _build_id_links none

%install
# Destination directories
mkdir -p %{buildroot}%{_bindir}
mkdir -p %{buildroot}%{_datadir}/%{name}
mkdir -p %{buildroot}%{_datadir}/applications
mkdir -p %{buildroot}%{_datadir}/appdata
mkdir -p %{buildroot}%{_datadir}/mime/packages
mkdir -p %{buildroot}%{_datadir}/pixmaps
mkdir -p %{buildroot}%{_datadir}/bash-completion/completions
mkdir -p %{buildroot}%{_datadir}/zsh/site-functions
# Application
cp -r usr/share/%{name}/* %{buildroot}%{_datadir}/%{name}
ln -s %{_datadir}/%{name}/bin/%{name} %{buildroot}%{_bindir}/%{name}
# Support files
cp -r usr/share/applications/%{name}.desktop %{buildroot}%{_datadir}/applications
cp -r usr/share/applications/%{name}-url-handler.desktop %{buildroot}%{_datadir}/applications
cp -r usr/share/appdata/%{name}.appdata.xml %{buildroot}%{_datadir}/appdata
cp -r usr/share/mime/packages/%{name}-workspace.xml %{buildroot}%{_datadir}/mime/packages/%{name}-workspace.xml
cp -r usr/share/pixmaps/@@ICON@@.png %{buildroot}%{_datadir}/pixmaps
# Shell completions
cp usr/share/bash-completion/completions/%{name} %{buildroot}%{_datadir}/bash-completion/completions/%{name}
cp usr/share/zsh/site-functions/_%{name} %{buildroot}%{_datadir}/zsh/site-functions/_%{name}

%post
# Remove the legacy bin command if this is the stable build
if [ "%{name}" = "code" ]; then
	rm -f /usr/local/bin/code
fi

# Register yum repository
# TODO: #229: Enable once the yum repository is signed
#if [ "@@NAME@@" != "code-oss" ]; then
#	if [ -d "/etc/yum.repos.d" ]; then
#		REPO_FILE=/etc/yum.repos.d/@@NAME@@.repo
#		rm -f $REPO_FILE
#		echo -e "[@@NAME@@]\nname=@@NAME_LONG@@\nbaseurl=@@UPDATEURL@@/api/rpm/@@QUALITY@@/@@ARCHITECTURE@@/rpm" > $REPO_FILE
#	fi
#fi

# Update mimetype database to pickup workspace mimetype
update-mime-database %{_datadir}/mime &> /dev/null || :

%postun
# Update mimetype database for removed workspace mimetype
update-mime-database %{_datadir}/mime &> /dev/null || :

%files
%defattr(-,root,root)
%attr(4755, root, root) %{_datadir}/%{name}/chrome-sandbox

%{_bindir}/%{name}
%{_datadir}/%{name}/
%{_datadir}/applications/%{name}.desktop
%{_datadir}/applications/%{name}-url-handler.desktop
%{_datadir}/appdata/%{name}.appdata.xml
%{_datadir}/mime/packages/%{name}-workspace.xml
%{_datadir}/pixmaps/@@ICON@@.png
%{_datadir}/bash-completion/completions/%{name}
%{_datadir}/zsh/site-functions/_%{name}
