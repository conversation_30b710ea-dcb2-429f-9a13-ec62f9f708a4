#!/usr/bin/env bash
#
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.

# Symlink bin command to /usr/bin
rm -f /usr/bin/@@NAME@@
ln -s /usr/share/@@NAME@@/bin/@@NAME@@ /usr/bin/@@NAME@@

# Register code in the alternatives system
# Priority of 0 should never make code the default editor in auto mode as most
# developers would prefer a terminal editor as the default.
update-alternatives --install /usr/bin/editor editor /usr/bin/@@NAME@@ 0

# Install the desktop entry
if hash update-desktop-database 2>/dev/null; then
	update-desktop-database
fi

# Update mimetype database to pickup workspace mimetype
if hash update-mime-database 2>/dev/null; then
	update-mime-database /usr/share/mime
fi

if [ "@@NAME@@" != "code-oss" ]; then
	# Remove the legacy bin command if this is the stable build
	if [ "@@NAME@@" = "code" ]; then
		rm -f /usr/local/bin/code
	fi

	# Register apt repository
	eval $(apt-config shell APT_SOURCE_PARTS Dir::Etc::sourceparts/d)
	CODE_SOURCE_PART=${APT_SOURCE_PARTS}vscode.list

	eval $(apt-config shell APT_TRUSTED_PARTS Dir::Etc::trustedparts/d)
	CODE_TRUSTED_PART=${APT_TRUSTED_PARTS}microsoft.gpg

	# Install repository source list
	WRITE_SOURCE=0
	if [ ! -f $CODE_SOURCE_PART ] && [ ! -f /etc/rpi-issue ]; then
		# Write source list if it does not exist and we're not running on Raspberry Pi OS
		WRITE_SOURCE=1
	elif grep -Eq "http:\/\/packages\.microsoft\.com\/repos\/vscode" $CODE_SOURCE_PART; then
		# Migrate from old repository
		WRITE_SOURCE=1
	elif grep -q "# disabled on upgrade to" $CODE_SOURCE_PART; then
		# Write source list if it was disabled by OS upgrade
		WRITE_SOURCE=1
	fi

	if [ "$WRITE_SOURCE" -eq "1" ]; then
		echo "### THIS FILE IS AUTOMATICALLY CONFIGURED ###
# You may comment out this entry, but any other modifications may be lost.
deb [arch=amd64,arm64,armhf] http://packages.microsoft.com/repos/code stable main" > $CODE_SOURCE_PART

		# Sourced from https://packages.microsoft.com/keys/microsoft.asc
		if [ ! -f $CODE_TRUSTED_PART ]; then
			echo "-----BEGIN PGP PUBLIC KEY BLOCK-----
Version: GnuPG v1.4.7 (GNU/Linux)

mQENBFYxWIwBCADAKoZhZlJxGNGWzqV+1OG1xiQeoowKhssGAKvd+buXCGISZJwT
LXZqIcIiLP7pqdcZWtE9bSc7yBY2MalDp9Liu0KekywQ6VVX1T72NPf5Ev6x6DLV
7aVWsCzUAF+eb7DC9fPuFLEdxmOEYoPjzrQ7cCnSV4JQxAqhU4T6OjbvRazGl3ag
OeizPXmRljMtUUttHQZnRhtlzkmwIrUivbfFPD+fEoHJ1+uIdfOzZX8/oKHKLe2j
H632kvsNzJFlROVvGLYAk2WRcLu+RjjggixhwiB+Mu/A8Tf4V6b+YppS44q8EvVr
M+QvY7LNSOffSO6Slsy9oisGTdfE39nC7pVRABEBAAG0N01pY3Jvc29mdCAoUmVs
ZWFzZSBzaWduaW5nKSA8Z3Bnc2VjdXJpdHlAbWljcm9zb2Z0LmNvbT6JATUEEwEC
AB8FAlYxWIwCGwMGCwkIBwMCBBUCCAMDFgIBAh4BAheAAAoJEOs+lK2+EinPGpsH
/32vKy29Hg51H9dfFJMx0/a/F+5vKeCeVqimvyTM04C+XENNuSbYZ3eRPHGHFLqe
MNGxsfb7C7ZxEeW7J/vSzRgHxm7ZvESisUYRFq2sgkJ+HFERNrqfci45bdhmrUsy
7SWw9ybxdFOkuQoyKD3tBmiGfONQMlBaOMWdAsic965rvJsd5zYaZZFI1UwTkFXV
KJt3bp3Ngn1vEYXwijGTa+FXz6GLHueJwF0I7ug34DgUkAFvAs8Hacr2DRYxL5RJ
XdNgj4Jd2/g6T9InmWT0hASljur+dJnzNiNCkbn9KbX7J/qK1IbR8y560yRmFsU+
NdCFTW7wY0Fb1fWJ+/KTsC4=
=J6gs
-----END PGP PUBLIC KEY BLOCK-----
" | gpg --dearmor > $CODE_TRUSTED_PART
		fi
	fi
fi
